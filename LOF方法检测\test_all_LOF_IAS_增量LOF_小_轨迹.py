import pandas as pd
import torch
from sklearn.preprocessing import MinMaxScaler, StandardScaler
import os
import sys
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import json
from sklearn.neighbors import LocalOutlierFactor
from sklearn.metrics import pairwise_distances

# --- 新增：动态将项目根目录添加到Python路径 ---
# 获取当前脚本的绝对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录 (假设此脚本位于根目录下的 'LOF方法检测' 文件夹中)
project_root = os.path.dirname(current_dir)
# 将项目根目录添加到sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)


# --- 1. 修改: 从轨迹预测脚本中导入配置 ---
from myinformer_轨迹 import TrainingConfig, Namespace
from models import Informer_convgru_优化轨迹 as Informer
from utils.timefeatures import time_features


# 解决Matplotlib画图中文显示问题
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use("ggplot")

# --- 优化: 基于小批量的流式LOF实现 ---
class StreamLOF:
    def __init__(self, n_neighbors=20, window_size=500, batch_size=10):
        """
        一个基于小批量处理和滑动窗口的流式LOF实现。
        """
        self.n_neighbors = n_neighbors
        self.window_size = window_size
        self.batch_size = batch_size
        self.data_window = []
        self.batch_buffer = []
        self.lof_scores = []
        # 使用scikit-learn的LOF作为核心计算引擎
        self.lof = LocalOutlierFactor(n_neighbors=n_neighbors, metric='euclidean', novelty=False)

    def add_point(self, point):
        """向缓冲区添加一个新点，并在达到批量大小时触发处理。"""
        self.batch_buffer.append(point)
        if len(self.batch_buffer) >= self.batch_size:
            self._process_batch()

    def _process_batch(self):
        """处理当前缓冲区中的数据批次。"""
        # 将新批次添加到主数据窗口
        self.data_window.extend(self.batch_buffer)

        # 如果窗口大小超过限制，则移除最旧的数据
        if len(self.data_window) > self.window_size:
            num_to_remove = len(self.data_window) - self.window_size
            self.data_window = self.data_window[num_to_remove:]
        
        print(f"  Processing batch... Current window size: {len(self.data_window)}")
        # 在整个当前窗口上运行高效的LOF计算
        self.lof.fit(self.data_window)
        
        # LOF分数越小越异常，我们取其相反数，使其越高越异常
        all_scores = -self.lof.negative_outlier_factor_

        # 我们只关心最新一个批次的分数
        new_scores = all_scores[-len(self.batch_buffer):]
        self.lof_scores.extend(new_scores)
        
        # 清空缓冲区
        self.batch_buffer = []

    def finalize(self):
        """处理缓冲区中剩余的任何数据点。"""
        if self.batch_buffer:
            self._process_batch()

    def get_scores(self):
        """返回所有处理过的点的LOF分数列表。"""
        return self.lof_scores


def load_and_process_segmented_data_for_test(file_path, config, scaler, feature_cols):
    """
    一个专门为 "多对多" 轨迹预测+异常检测脚本修改的数据处理函数。
    """
    print(f"--- 开始处理测试文件: {file_path} ---")
    df = pd.read_csv(file_path)

    # --- 新增: 检查并加载真实异常标签 ---
    if 'is_anomaly' in df.columns:
        print("发现 'is_anomaly' 列，将用作异常检测评估的真实标签。")
        anomaly_labels = df['is_anomaly'].values
    else:
        print("未发现 'is_anomaly' 列，将假设所有点均为正常点。")
        anomaly_labels = np.zeros(len(df))

    df['datetime'] = pd.to_datetime(df[config.time_column], format='%H:%M:%S')

    df_stamp = df[['datetime']].copy()
    df_stamp.columns = ['date']
    time_marks = time_features(pd.DataFrame(df_stamp), timeenc=1, freq=config.freq)

    df_features = df[feature_cols]
    scaled_features = scaler.transform(df_features)
    
    # --- 2. 修改: 适配多目标索引 ---
    try:
        if isinstance(config.target_feature, str):
            target_cols_indices = [feature_cols.index(config.target_feature)]
        else:
            target_cols_indices = [feature_cols.index(t) for t in config.target_feature]
    except ValueError as e:
        raise ValueError(f"错误: 目标特征 '{e.args[0]}' 不在特征列表 feature_cols 中。")


    print("正在根据 '起点' 和 '终点' 标记识别飞行段...")
    segments = []
    segment_start_index = -1
    for i, row in df.iterrows():
        marker = str(row[config.segment_marker_column])
        if '起点' in marker:
            segment_start_index = i
        elif '终点' in marker and segment_start_index != -1:
            segment_df = df.loc[segment_start_index:i].copy()
            segments.append(segment_df)
            segment_start_index = -1
            
    print(f"在文件 {file_path} 中识别出 {len(segments)} 个有效飞行段。")
    
    all_x, all_y, all_x_mark, all_y_mark = [], [], [], []
    segment_window_counts = [] 
    all_target_times = []
    all_ground_truth = [] # <--- 新增

    for segment_df in segments:
        segment_indices = segment_df.index
        current_segment_features = scaled_features[segment_indices]
        current_segment_marks = time_marks[segment_indices] if time_marks is not None else np.empty_like(scaled_features[segment_indices]) # Ensure time_marks is not None
        
        total_sequence_length = config.seq_len + config.pred_len
        
        if len(current_segment_features) < total_sequence_length:
            segment_window_counts.append(0)
            continue

        num_windows_in_segment = 0
        for i in range(len(current_segment_features) - total_sequence_length + 1):
            x_seq = current_segment_features[i : i + config.seq_len]
            x_mark_seq = current_segment_marks[i : i + config.seq_len]
            y_start_index = i + config.seq_len - config.label_len
            y_end_index = i + total_sequence_length
            
            # --- 3. 修改: y_seq只提取目标特征那一列 ---
            y_seq_full = current_segment_features[y_start_index : y_end_index]
            y_seq_target = y_seq_full[:, target_cols_indices]
            
            y_mark_seq = current_segment_marks[y_start_index : y_end_index]

            all_x.append(x_seq)
            all_x_mark.append(x_mark_seq)
            all_y.append(y_seq_target) # <--- 使用只包含目标特征的y
            all_y_mark.append(y_mark_seq)
            
            target_original_index = segment_df.index[i + total_sequence_length - 1]
            all_target_times.append(df.loc[target_original_index, 'datetime'])
            
            # --- 新增: 收集真实标签 ---
            all_ground_truth.append(anomaly_labels[target_original_index])
            
            num_windows_in_segment += 1
        
        segment_window_counts.append(num_windows_in_segment)
            
    total_windows = len(all_x)
    print(f"文件 {file_path} 处理完毕，共生成 {total_windows} 个有效窗口。")

    final_x = torch.tensor(np.array(all_x), dtype=torch.float32)
    final_y = torch.tensor(np.array(all_y), dtype=torch.float32)
    final_x_mark = torch.tensor(np.array(all_x_mark), dtype=torch.float32)
    final_y_mark = torch.tensor(np.array(all_y_mark), dtype=torch.float32)
    final_ground_truth = torch.tensor(np.array(all_ground_truth), dtype=torch.long)
    
    return final_x, final_y, final_x_mark, final_y_mark, segment_window_counts, all_target_times, final_ground_truth


def test_and_plot_predictions(model, loader, scaler, config, device, output_dir, feature_cols, segment_lengths=None, target_times=None):
    """
    --- 4. 重构: 整个函数被重构以适应多目标轨迹预测和异常检测 ---
    """
    print(f"\n--- 开始在测试集上评估和预测目标: {config.target_feature} ---")
    model.eval()
    all_preds, all_trues, all_ground_truth = [], [], []
    with torch.no_grad():
        for i, (batch_x, batch_y, batch_x_mark, batch_y_mark, batch_ground_truth) in enumerate(loader):
            batch_x, batch_y, batch_x_mark, batch_y_mark = \
                batch_x.to(device), batch_y.to(device), batch_x_mark.to(device), batch_y_mark.to(device)
            
            dec_inp = torch.zeros_like(batch_y[:, -config.pred_len:, :]).float()
            dec_inp = torch.cat([batch_y[:, :config.label_len, :], dec_inp], dim=1).float().to(device)
            
            outputs = model(batch_x, batch_x_mark, dec_inp, batch_y_mark)
            
            true_targets = batch_y[:, -config.pred_len:, :]
            pred_targets = outputs
            
            all_preds.append(pred_targets.detach().cpu().numpy())
            all_trues.append(true_targets.detach().cpu().numpy())
            all_ground_truth.append(batch_ground_truth.cpu().numpy())

    preds = np.concatenate(all_preds, axis=0)
    trues = np.concatenate(all_trues, axis=0)
    ground_truth_labels = np.concatenate(all_ground_truth, axis=0).flatten()

    if preds.ndim == 3:
        preds = preds[:, -1, :]
        trues = trues[:, -1, :]


    # --- 核心修改: 将异常判据从 "瞬时误差" 改为 "轨迹欧氏距离" ---
    print("\n--- 策略: 基于预测轨迹点与真实轨迹点之间的欧氏距离进行异常检测 ---")

    # 1. 反归一化整个预测轨迹
    num_features = len(feature_cols)
    target_cols_indices = [feature_cols.index(t) for t in config.target_feature]
    
    dummy_preds = np.zeros((preds.shape[0], num_features))
    dummy_trues = np.zeros((trues.shape[0], num_features))

    dummy_preds[:, target_cols_indices] = preds
    dummy_trues[:, target_cols_indices] = trues

    preds_unscaled_full = scaler.inverse_transform(dummy_preds)
    trues_unscaled_full = scaler.inverse_transform(dummy_trues)
    
    # 2. 计算每个窗口的欧氏距离作为异常分数
    errors = np.linalg.norm(trues_unscaled_full[:, target_cols_indices] - preds_unscaled_full[:, target_cols_indices], axis=1)
    print(f"已计算所有窗口的轨迹欧氏距离，将作为LOF算法的输入。距离误差范围: [{errors.min():.6f}, {errors.max():.6f}]")


    # --- 流式LOF 异常检测模块 ---
    print("\n--- 正在使用基于小批量的流式 LOF 算法进行异常检测 ---")

    # --- 特征工程更新: 基于轨迹欧氏距离构建特征 ---
    print("创建二维特征: [轨迹欧氏距离, 距离变化率]")
    error_gradients = np.gradient(errors)
    features_for_lof = np.stack([errors, error_gradients], axis=1)

    # --- 对(二维)误差特征进行标准化 ---
    print("正在对二维误差特征进行Z-score标准化...")
    scaler_lof = StandardScaler()
    errors_scaled = scaler_lof.fit_transform(features_for_lof)
    
    # --- 新增: 从NPSR脚本移植过来的高效F1阈值搜索函数 (带Precision约束) ---
    def get_best_f1_threshold(scores, labels, min_precision=0.8):
        """
        高效F1阈值搜索算法。
        在满足最小精确度要求的前提下，寻找最优F1分数对应的阈值。
        假设分数越高越异常。
        """
        scores = np.asarray(scores).flatten()
        labels = labels.flatten()
        
        # LOF分数越高越异常，所以按分数升序排序
        combined = sorted(zip(scores, labels), key=lambda x: x[0])
        sorted_scores, sorted_labels = zip(*combined)
        sorted_labels = np.array(sorted_labels)
        
        total_positives = np.sum(sorted_labels)
        if total_positives == 0:
            print("警告: 真实标签中没有异常点，无法计算F1分数。")
            return -1, -1

        tp = total_positives
        fp = len(labels) - total_positives
        fn = 0
        
        best_f1 = -1
        best_threshold = -1

        # 从将所有点都判为异常开始，逐渐提高阈值
        for i in range(len(sorted_scores) - 1):
            # 阈值移动，一个点从异常堆移动到正常堆
            if sorted_labels[i] == 1:  # 如果这个点是真异常
                tp -= 1
                fn += 1
            else:  # 如果这个点是真正常
                fp -= 1

            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            
            # --- 新增约束: 只有当精确度满足要求时才考虑 ---
            if precision > min_precision:
                recall = tp / (tp + fn) if (tp + fn) > 0 else 0
                f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
                
                # 更新最优F1和阈值
                if f1 >= best_f1:
                    # 只有当分数变化时才更新阈值
                    if sorted_scores[i] < sorted_scores[i+1]:
                        best_f1 = f1
                        best_threshold = (sorted_scores[i] + sorted_scores[i+1]) / 2
        
        if best_threshold == -1:
            print(f"警告: 在 Precision > {min_precision} 的约束下，未找到任何有效的阈值。")

        return best_f1, best_threshold

    # --- 优化策略更新: 动态搜索最优 n_neighbors 和 阈值 以最大化F1分数 ---
    print("\n--- 开始动态搜索最优 n_neighbors 和 阈值以最大化F1分数 (要求 Precision > 0.8) ---")
    
    global_best_f1 = -1
    best_n_neighbors = -1
    best_threshold = -1
    best_window_size = -1
    final_anomaly_scores = None

    # 1. 遍历 n_neighbors 和 window_size 的候选值
    for n_neighbors_candidate in range(10, 41, 5):
        # 窗口大小至少是邻居数的两倍
        for window_size_candidate in [100, 200, 300]:
            if window_size_candidate <= 2 * n_neighbors_candidate:
                continue

            print(f"\n--- 正在测试 n_neighbors={n_neighbors_candidate}, window_size={window_size_candidate} ---")
            
            # 2. 使用流式LOF模拟计算分数
            stream_lof = StreamLOF(n_neighbors=n_neighbors_candidate, window_size=window_size_candidate, batch_size=10)
            
            for point in errors_scaled:
                stream_lof.add_point(point)
            stream_lof.finalize() # 处理剩余的点
            
            current_anomaly_scores = np.array(stream_lof.get_scores())
            
            # 确保分数数量和标签数量一致
            if len(current_anomaly_scores) != len(ground_truth_labels):
                 print(f"  警告: 分数数量 ({len(current_anomaly_scores)}) 与标签数量 ({len(ground_truth_labels)}) 不匹配，跳过此组合。")
                 continue
            
            # 3. 使用高效函数找到当前组合下的最优F1和对应阈值
            current_f1, current_threshold = get_best_f1_threshold(current_anomaly_scores, ground_truth_labels, min_precision=0.8)

            if current_threshold != -1 and current_f1 != -1:
                print(f"  结果: (P>0.8) 最优F1={current_f1:.4f}")

                # 4. 如果当前F1分数是全局最优的，则更新全局参数
                if current_f1 > global_best_f1:
                    global_best_f1 = current_f1
                    best_n_neighbors = n_neighbors_candidate
                    best_window_size = window_size_candidate
                    best_threshold = current_threshold
                    final_anomaly_scores = current_anomaly_scores
            else:
                print(f"  结果: 未找到满足P>0.8的有效阈值。")

    print("\n--- 参数搜索完成 ---")
    if best_n_neighbors != -1:
        feature_threshold = best_threshold
        anomaly_scores = final_anomaly_scores
        print(f"最终采用的最优参数: n_neighbors={best_n_neighbors}, window_size={best_window_size}, 对应的LOF分数阈值为 > {feature_threshold:.4f}")
        print(f"在此参数下达到的全局最优F1分数为: {global_best_f1:.4f}")
    else:
        print("警告: 未能找到任何满足P>0.8的有效参数组合，将使用默认值进行预测。")
        # 回退逻辑
        lof = LocalOutlierFactor(n_neighbors=35, metric='euclidean', novelty=False)
        lof.fit(errors_scaled)
        anomaly_scores = -lof.negative_outlier_factor_
        feature_threshold = np.median(anomaly_scores) if anomaly_scores.size > 0 else 0.5
        
    # --- 使用最优阈值计算最终的异常点 ---
    # LOF分数越高越异常
    predicted_anomalies = (anomaly_scores > feature_threshold).astype(int) if anomaly_scores is not None else np.zeros_like(ground_truth_labels)
    
    TP = np.sum((predicted_anomalies == 1) & (ground_truth_labels == 1))
    FP = np.sum((predicted_anomalies == 1) & (ground_truth_labels == 0))
    TN = np.sum((predicted_anomalies == 0) & (ground_truth_labels == 0))
    FN = np.sum((predicted_anomalies == 0) & (ground_truth_labels == 1))
    
    # --- 新增: 计算评估指标 ---
    TPR = TP / (TP + FN) if (TP + FN) > 0 else 0  # 检测率 (Recall)
    FPR = FP / (FP + TN) if (FP + TN) > 0 else 0  # 误检率
    Pre = TP / (TP + FP) if (TP + FP) > 0 else 0  # 精确度
    Acc = (TP + TN) / (TP + TN + FP + FN) if (TP + TN + FP + FN) > 0 else 0 # 准确率
    F1 = 2 * Pre * TPR / (Pre + TPR) if (Pre + TPR) > 0 else 0 # F1分数
    
    print("\n--- 异常检测评估指标 ---")
    print(f"TP: {TP}, FP: {FP}, TN: {TN}, FN: {FN}")
    print(f"检测率 (TPR/Recall): {TPR:.4f}")
    print(f"误检率 (FPR): {FPR:.4f}")
    print(f"精确度 (Precision): {Pre:.4f}")
    print(f"准确率 (Accuracy): {Acc:.4f}")
    print(f"F1 分数: {F1:.4f}")
    
    anomaly_metrics = {
        'TP': TP, 'FP': FP, 'TN': TN, 'FN': FN,
        'TPR': TPR, 'FPR': FPR, 'Precision': Pre,
        'Accuracy': Acc, 'F1_Score': F1,
        'Threshold': feature_threshold
    }
    
    # --- 保存异常检测指标到单独的CSV
    anomaly_metrics_df = pd.DataFrame([anomaly_metrics])
    anomaly_metrics_save_path = os.path.join(output_dir, 'test_anomaly_evaluation_metrics.csv')
    anomaly_metrics_df.to_csv(anomaly_metrics_save_path, index=False, encoding='utf-8-sig')
    print(f"异常检测评估指标已保存到: {anomaly_metrics_save_path}")

    all_anomalies_report = []
    
    # --- 6. 简化: 计算每个目标的评估指标 ---
    metrics_data = []
    for i, feature in enumerate(config.target_feature):
        target_idx_in_all = feature_cols.index(feature)
        r2 = r2_score(trues_unscaled_full[:, target_idx_in_all], preds_unscaled_full[:, target_idx_in_all])
        mse = mean_squared_error(trues_unscaled_full[:, target_idx_in_all], preds_unscaled_full[:, target_idx_in_all])
        mae = mean_absolute_error(trues_unscaled_full[:, target_idx_in_all], preds_unscaled_full[:, target_idx_in_all])
        print(f"测试集评估结果 ({feature}) - R2: {r2:.4f}, MSE: {mse:.6f}, MAE: {mae:.6f}")
        metrics_data.append({'Feature': feature, 'R2': r2, 'MSE': mse, 'MAE': mae})

    
    # --- 7. 修改: 绘制带异常标记的轨迹图 ---
    valid_segments_to_plot = [l for l in segment_lengths if l > 0] if segment_lengths is not None else []
    n_segments = len(valid_segments_to_plot)

    if n_segments > 0:
        if n_segments == 1: n_cols = 1; figsize = (8, 8)
        else: n_cols = 2; figsize = (15, ((n_segments + 1) // 2) * 7)
        n_rows = (n_segments + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize, squeeze=False)
        fig.suptitle(f'TEST SET: Predicted vs Real Trajectory with Anomaly Detection', fontsize=16)
        
        current_idx = 0
        plot_idx = 0
        
        lon_idx = feature_cols.index('Longitude')
        lat_idx = feature_cols.index('Latitude')

        for seg_idx, length in enumerate(segment_lengths if segment_lengths is not None else []):
            if length == 0: continue
            
            segment_trues_lon = trues_unscaled_full[current_idx : current_idx + length, lon_idx]
            segment_preds_lon = preds_unscaled_full[current_idx : current_idx + length, lon_idx]
            segment_trues_lat = trues_unscaled_full[current_idx : current_idx + length, lat_idx]
            segment_preds_lat = preds_unscaled_full[current_idx : current_idx + length, lat_idx]

            row, col = plot_idx // n_cols, plot_idx % n_cols
            ax = axes[row, col]

            # 绘制轨迹
            ax.plot(segment_trues_lon, segment_trues_lat, 'o-', markersize=2, label='Real Trajectory', color='cornflowerblue', zorder=1)
            ax.plot(segment_preds_lon, segment_preds_lat, 'x-', markersize=2, label='Predicted Trajectory', alpha=0.6, color='darkorange', zorder=2)
            
            # --- 新增: 在图上标记异常点 (基于LOF结果) ---
            segment_predicted_anomalies = predicted_anomalies[current_idx : current_idx + length]
            anomaly_indices_in_segment = np.where(segment_predicted_anomalies == 1)[0]
            
            if len(anomaly_indices_in_segment) > 0:
                anomaly_lon = segment_trues_lon[anomaly_indices_in_segment]
                anomaly_lat = segment_trues_lat[anomaly_indices_in_segment]
                ax.scatter(anomaly_lon, anomaly_lat, color='red', s=50, zorder=5, label='Detected Anomaly', edgecolors='black')
                
                # 记录异常信息到报告
                segment_lof_scores = anomaly_scores[current_idx : current_idx + length] if anomaly_scores is not None else np.array([])
                for anom_idx in anomaly_indices_in_segment:
                    all_anomalies_report.append({
                        'Time': target_times[current_idx + anom_idx].strftime('%H:%M:%S'),
                        'Real_Lat': segment_trues_lat[anom_idx],
                        'Real_Lon': segment_trues_lon[anom_idx],
                        'Pred_Lat': segment_preds_lat[anom_idx],
                        'Pred_Lon': segment_preds_lon[anom_idx],
                        'AnomalyScore': segment_lof_scores[anom_idx],
                        'Threshold': feature_threshold
                    })

            ax.set_title(f"Test Segment {seg_idx+1}")
            ax.set_xlabel('Longitude')
            ax.set_ylabel('Latitude')

            handles, labels = ax.get_legend_handles_labels()
            by_label = dict(zip(labels, handles))
            ax.legend(by_label.values(), by_label.keys())
            
            ax.grid(True)
            ax.set_aspect('equal', adjustable='box')

            current_idx += length
            plot_idx += 1

        for j in range(plot_idx, n_rows * n_cols): fig.delaxes(axes.flatten()[j])
        plt.tight_layout(rect=(0.0, 0.03, 1.0, 0.95))
        save_path_img = os.path.join(output_dir, f'test_prediction_plot_with_anomalies.png')
        plt.savefig(save_path_img)
        print(f"带异常标记的轨迹预测图已保存到: {save_path_img}")
        plt.close(fig)

    # --- 新增：为LOF分类结果生成散点图 (修改为用户指定样式) ---
    fig_lof, ax_lof = plt.subplots(figsize=(8, 6))
    indices = np.arange(len(errors))

    # 为了绘图，将最终的0/1结果(predicted_anomalies)转换回-1/1格式
    # 异常(1) -> 1 (红色), 正常(0) -> -1 (蓝色) - 调整以匹配coolwarm
    plot_predictions = np.where(predicted_anomalies == 1, 1, -1)

    # 使用单一的scatter调用和cmap来匹配示例图的样式
    # 在plot_predictions中, 1是异常点(coolwarm中接近红色), -1是正常点(coolwarm中接近蓝色)
    scatter = ax_lof.scatter(indices, errors, c=plot_predictions, cmap='coolwarm', vmin=-1, vmax=1)

    ax_lof.set_title('Stream LOF Anomaly Detection on Trajectory Error')
    ax_lof.set_xlabel('Data Point Index')
    ax_lof.set_ylabel('Trajectory Euclidean Distance Error') # <-- 修改Y轴标签
    ax_lof.grid(True)
    
    # 创建图例
    legend_elements = [Line2D([0], [0], marker='o', color='w', label='Anomaly', markerfacecolor='red', markersize=8),
                       Line2D([0], [0], marker='o', color='w', label='Normal', markerfacecolor='blue', markersize=8)]
    ax_lof.legend(handles=legend_elements)


    save_path_lof_scatter = os.path.join(output_dir, f'test_lof_classification_plot_trajectory.png')
    plt.savefig(save_path_lof_scatter)
    print(f"LOF 分类检测图已保存到: {save_path_lof_scatter}")
    plt.close(fig_lof)


    # --- 8. 简化: 保存唯一的评估和预测结果 ---
    metrics_df = pd.DataFrame(metrics_data)
    metrics_save_path = os.path.join(output_dir, 'test_evaluation_metrics.csv')
    metrics_df.to_csv(metrics_save_path, index=False, encoding='utf-8-sig')
    print(f"\n评估指标已保存到: {metrics_save_path}")

    prediction_dfs = {'Time': [t.strftime('%H:%M:%S') for t in target_times] if target_times is not None else []}
    for feature in config.target_feature:
        target_idx_in_all = feature_cols.index(feature)
        prediction_dfs[f'Real_{feature}'] = trues_unscaled_full[:, target_idx_in_all]
        prediction_dfs[f'Predicted_{feature}'] = preds_unscaled_full[:, target_idx_in_all]

    results_df = pd.DataFrame(prediction_dfs)
    results_save_path = os.path.join(output_dir, 'test_predictions.csv')
    results_df.to_csv(results_save_path, index=False, encoding='utf-8-sig')
    print(f"预测结果CSV已保存到: {results_save_path}")

    # --- 新增: 保存异常检测报告CSV ---
    if all_anomalies_report:
        anomalies_df = pd.DataFrame(all_anomalies_report)
        anomalies_df.sort_values(by=['Time'], inplace=True)
        anomalies_save_path = os.path.join(output_dir, 'test_anomalies_report.csv')
        anomalies_df.to_csv(anomalies_save_path, index=False, encoding='utf-8-sig')
        print(f"异常检测报告已保存到: {anomalies_save_path}")
    else:
        print("在本次测试中未检测到异常点。")

if __name__ == '__main__':
    # --- 9. 修改: 主函数逻辑全面适配 "多对多" 轨迹预测+异常检测 ---
    config = TrainingConfig() # 使用 myinformer_轨迹.py 中的配置
    
    test_file_path = 'data/起落航线/anomaly.csv'
    
    # --- 核心修改: 将结果直接保存在 'LOF方法检测' 文件夹下 ---
    script_dir = os.path.dirname(os.path.abspath(__file__))
    test_results_dir = os.path.join(script_dir, 'test_results_trajectory_增量LOF')
    os.makedirs(test_results_dir, exist_ok=True)
    print(f"测试结果将保存到: '{test_results_dir}'")
    
    # 结果将保存在轨迹模型训练结果目录下
    model_dir = config.results_dir
    # 请确保这个路径指向你训练好的轨迹预测模型
    model_path = os.path.join(model_dir, f'best_model_{config.model_name}.pth')
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"未找到轨迹预测模型, 请确认路径正确: {model_path}")
    if not os.path.exists(test_file_path):
         raise FileNotFoundError(f"未找到测试集文件: {test_file_path}")

    # --- 加载训练集以拟合缩放器并获取特征列表 ---
    print("\n--- 在原始训练集上重新拟合缩放器和确定特征 ---")
    df_train_raw = pd.read_csv(config.train_file_path)
    
    # --- <修改> 直接使用训练配置中定义的特征列表 ---
    feature_cols = config.use_features
    
    # 安全检查：确保所有指定的特征都存在于DataFrame中
    available_cols = df_train_raw.columns.tolist()
    missing_in_df = [col for col in feature_cols if col not in available_cols]
    if missing_in_df:
        print(f"警告: 在训练数据中找不到以下指定的特征: {missing_in_df}")
        feature_cols = [col for col in feature_cols if col not in missing_in_df]
        config.use_features = feature_cols

    # 安全检查：确保目标特征在所选特征列表中
    if isinstance(config.target_feature, list):
        for t in config.target_feature:
            if t not in feature_cols:
                raise ValueError(f"致命错误: 目标特征 '{t}' 不在最终的 use_features 列表中。")
    elif config.target_feature not in feature_cols:
        raise ValueError(f"致命错误: 目标特征 '{config.target_feature}' 不在最终的 use_features 列表中。")


    scaler = MinMaxScaler()
    scaler.fit(df_train_raw[feature_cols])
    print(f"缩放器拟合成功，将使用以下 {len(feature_cols)} 个特征作为输入: {feature_cols}")
    print(f"测试目标为: {config.target_feature}")

    # --- 10. 修改: 确保在创建模型配置前更新动态参数 ---
    print("\n--- 准备模型配置 ---")
    # 在调用 to_informer_config() 之前，必须先设置好动态计算的参数
    config.data_dim = len(feature_cols)
    if isinstance(config.target_feature, str):
        config.target_dim_indices = [feature_cols.index(config.target_feature)]
    else:
        config.target_dim_indices = [feature_cols.index(t) for t in config.target_feature]

    # 创建 Informer 模型的配置对象
    informer_config_obj = config.to_informer_config()

    # 确认参数已正确传递
    if not hasattr(informer_config_obj, 'enc_in') or informer_config_obj.enc_in is None:
        raise ValueError("模型配置错误: 'enc_in' 未被设置或为 None。")
    if not hasattr(informer_config_obj, 'target_dim_indices'):
        raise ValueError("模型配置错误: 'target_dim_indices' 未被设置。")

    print(f"模型配置创建成功。输入维度(enc_in): {informer_config_obj.enc_in}, 目标索引(target_dim_indices): {getattr(informer_config_obj, 'target_dim_indices', 'Not Found')}")

    # --- 加载已训练的模型 ---
    print("\n--- 加载已训练的轨迹预测模型 ---")
    model = Informer.Model(informer_config_obj).to(device)
    
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.eval() 
    print(f"模型已从 {model_path} 加载")

    # --- 加载并处理测试数据 ---
    print("\n--- 处理测试数据 ---")
    test_x, test_y, test_x_mark, test_y_mark, test_segment_lengths, test_target_times, test_ground_truth = \
        load_and_process_segmented_data_for_test(test_file_path, config, scaler, feature_cols)

    test_dataset = TensorDataset(test_x, test_y, test_x_mark, test_y_mark, test_ground_truth)
    test_loader = DataLoader(test_dataset, batch_size=config.batch_size, shuffle=False)
    print("测试数据加载完毕。")

    # --- 在测试集上评估并可视化结果 ---
    test_and_plot_predictions(
        model=model,
        loader=test_loader,
        scaler=scaler,
        config=config,
        device=device,
        output_dir=test_results_dir,
        feature_cols=feature_cols,
        segment_lengths=test_segment_lengths,
        target_times=test_target_times,
    ) 