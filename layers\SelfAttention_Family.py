import torch
import torch.nn as nn
import numpy as np
from math import sqrt
from utils.masking import TriangularCausalMask, ProbMask
from reformer_pytorch import LSHSelfAttention
from einops import rearrange, repeat


class DSAttention(nn.Module):
    """
    De-stationary Attention (出自 Autoformer)
    一种针对非平稳时间序列的注意力机制变体。它通过引入两个可学习的参数 tau 和 delta，
    来动态地调整注意力分数的计算，从而更好地适应时间序列数据中均值和方差变化的特性。
    """

    def __init__(self, mask_flag=True, factor=5, scale=None, attention_dropout=0.1, output_attention=False):
        super(DSAttention, self).__init__()
        self.scale = scale
        self.mask_flag = mask_flag
        self.output_attention = output_attention
        self.dropout = nn.Dropout(attention_dropout)

    def forward(self, queries, keys, values, attn_mask, tau=None, delta=None):
        # queries: [B, L, H, E]  (L是查询序列长度)
        # keys:    [B, S, H, E]  (S是键/值序列长度)
        # values:  [B, S, H, D]
        B, L, H, E = queries.shape
        _, S, _, D = values.shape
        # scale: 缩放因子，防止 softmax 进入饱和区。默认为 1/sqrt(E)。
        scale = self.scale or 1. / sqrt(E)

        # tau 和 delta 是可学习的非平稳性调整参数
        # tau (Period-wise taus): 形状为 [B, 1, 1, 1], 作用于 Q，调整每个查询的整体重要性。
        tau = 1.0 if tau is None else tau.unsqueeze(1).unsqueeze(1)
        # delta (Series-wise deltas): 形状为 [B, 1, 1, S], 作用于 K，调整每个键的相对重要性。
        delta = 0.0 if delta is None else delta.unsqueeze(1).unsqueeze(1)

        # De-stationary Attention 的核心计算公式
        # 在标准的 QK^T 点积之后，乘以 tau 并加上 delta 来进行调整。
        scores = torch.einsum("blhe,bshe->bhls", queries, keys) * tau + delta

        if self.mask_flag:
            if attn_mask is None:
                attn_mask = TriangularCausalMask(B, L, device=queries.device)
            scores.masked_fill_(attn_mask.mask, -np.inf)

        # 计算注意力权重矩阵 A，并应用于 V
        A = self.dropout(torch.softmax(scale * scores, dim=-1))
        V = torch.einsum("bhls,bshd->blhd", A, values)

        if self.output_attention:
            return V.contiguous(), A
        else:
            return V.contiguous(), None


class FullAttention(nn.Module):
    """
    标准的全量注意力机制 (Vanilla Attention)。
    每个查询(Query)都会与所有的键(Key)进行点积计算，复杂度为 O(L*S)。
    """
    def __init__(self, mask_flag=True, factor=5, scale=None, attention_dropout=0.1, output_attention=False):
        super(FullAttention, self).__init__()
        self.scale = scale
        self.mask_flag = mask_flag
        self.output_attention = output_attention
        self.dropout = nn.Dropout(attention_dropout)

    def forward(self, queries, keys, values, attn_mask, tau=None, delta=None):
        B, L, H, E = queries.shape
        _, S, _, D = values.shape
        scale = self.scale or 1. / sqrt(E)

        # 核心计算：使用 einsum 高效计算 Q 和 K 的批量点积。
        # "blhe,bshe->bhls" 解释:
        # b: batch, l: L_query, h: n_heads, e: d_head, s: L_key
        # 计算 queries(b,l,h,e) 和 keys(b,s,h,e) 的点积，得到 scores(b,h,l,s)
        scores = torch.einsum("blhe,bshe->bhls", queries, keys)

        # 如果需要，应用掩码 (例如，在解码器中防止看到未来的信息)
        if self.mask_flag:
            if attn_mask is None:
                attn_mask = TriangularCausalMask(B, L, device=queries.device)
            scores.masked_fill_(attn_mask.mask, -np.inf)

        A = self.dropout(torch.softmax(scale * scores, dim=-1))
        V = torch.einsum("bhls,bshd->blhd", A, values)

        if self.output_attention:
            return V.contiguous(), A
        else:
            return V.contiguous(), None


class ProbAttention(nn.Module):
    """
    Informer的核心：ProbSparse Attention (概率稀疏注意力)。
    其核心思想是，大多数注意力分数对最终结果的贡献很小，形成了"长尾分布"。
    因此，我们只需要计算那些"重要的"查询(Query)与所有键(Key)的点积，
    而忽略那些"不重要的"查询，从而将复杂度从 O(L*S) 降低到 O(L log S)。
    """
    def __init__(self, mask_flag=True, factor=5, scale=None, attention_dropout=0.1, output_attention=False):
        super(ProbAttention, self).__init__()
        self.factor = factor
        self.scale = scale
        self.mask_flag = mask_flag
        self.output_attention = output_attention
        self.dropout = nn.Dropout(attention_dropout)

    def _prob_QK(self, Q, K, sample_k, n_top):
        """
        核心函数：计算稀疏 QK 矩阵。
        参数:
            Q (Tensor): 查询, 形状 [B, H, L_Q, E]。
            K (Tensor): 键, 形状 [B, H, L_K, E]。
            sample_k (int): 对于每个查询，需要随机采样的键的数量 (c * log(L_K))。
            n_top (int): 需要选出的"重要"查询的数量 (c * log(L_Q))。
        """
        # 获取 Q, K 的形状信息
        B, H, L_K, E = K.shape
        _, _, L_Q, _ = Q.shape

        # --- 步骤 1: 近似计算所有查询的"重要性得分" ---
        # 为了避免计算完整的 QK^T (O(L_Q * L_K))，我们通过随机抽样来近似计算。
        # 1.1 将 K 扩展，为每个 Q 准备一份完整的 K 以便采样。
        K_expand = K.unsqueeze(-3).expand(B, H, L_Q, L_K, E)
        # 1.2 为 L_Q 个查询，每个都随机采样 sample_k 个键的索引。
        index_sample = torch.randint(L_K, (L_Q, sample_k))
        # 1.3 根据索引，从扩展后的 K 中采样出对应的 K_sample。
        K_sample = K_expand[:, :, torch.arange(L_Q).unsqueeze(1), index_sample, :]
        # 1.4 计算每个 Q 与它对应的 K_sample 的点积。
        Q_K_sample = torch.matmul(Q.unsqueeze(-2), K_sample.transpose(-2, -1)).squeeze(-2)

        # --- 步骤 2: 计算稀疏性度量 (Sparsity Measurement) ---
        # 这是论文中的核心公式 M(q, K) = max_j(q*k_j/sqrt(d)) - mean_j(q*k_j/sqrt(d))
        # M 越大，说明该 Q 的注意力分布越"尖锐"(稀疏)，因此越"重要"。
        M = Q_K_sample.max(-1)[0] - torch.div(Q_K_sample.sum(-1), L_K)
        # 1.5 选出得分最高的 n_top 个查询的索引。
        M_top = M.topk(n_top, sorted=False)[1]

        # --- 步骤 3: 只计算"重要"查询的完整注意力分数 ---
        # 1.6 根据 M_top 索引，从原始的 Q 中选出那些"重要"的查询 Q_reduce。
        Q_reduce = Q[torch.arange(B)[:, None, None],
                   torch.arange(H)[None, :, None],
                   M_top, :]
        # 1.7 计算这些被选中的 Q_reduce 与所有的 K 的点积。
        # 这里的计算量是 O(n_top * L_K) = O(log(L_Q) * L_K)，大大减少了计算量。
        Q_K = torch.matmul(Q_reduce, K.transpose(-2, -1))

        return Q_K, M_top

    def _get_initial_context(self, V, L_Q):
        """
        为所有查询初始化一个"上下文"向量。
        对于那些"不重要"的查询，我们将直接使用这个平均上下文作为它们的输出。
        """
        B, H, L_V, D = V.shape
        if not self.mask_flag:
            # 如果不使用掩码，初始上下文就是所有 Value 向量的均值。
            V_sum = V.mean(dim=-2)
            contex = V_sum.unsqueeze(-2).expand(B, H, L_Q, V_sum.shape[-1]).clone()
        else: # 如果使用掩码 (例如解码器自注意力)
            # 必须 L_Q == L_V
            assert (L_Q == L_V)
            # 初始上下文是 V 的累加和，以确保因果性。
            contex = V.cumsum(dim=-2)
        return contex

    def _update_context(self, context_in, V, scores, index, L_Q, attn_mask):
        """
        使用计算出的稀疏注意力分数，更新"重要"查询的上下文向量。
        """
        B, H, L_V, D = V.shape

        if self.mask_flag:
            # 如果需要，应用 ProbMask，它会屏蔽掉非重要查询的注意力。
            attn_mask = ProbMask(B, H, L_Q, index, scores, device=V.device)
            scores.masked_fill_(attn_mask.mask, -np.inf)

        # 计算注意力权重
        attn = torch.softmax(scores, dim=-1)

        # 使用注意力权重更新"重要"查询的上下文
        context_in[torch.arange(B)[:, None, None],
                   torch.arange(H)[None, :, None],
                   index, :] = torch.matmul(attn, V).type_as(context_in)
        
        # 如果需要输出注意力图，则构造一个稀疏的注意力图。
        if self.output_attention:
            attns = (torch.ones([B, H, L_V, L_V]) / L_V).type_as(attn).to(attn.device)
            attns[torch.arange(B)[:, None, None], torch.arange(H)[
                                                  None, :, None], index, :] = attn
            return context_in, attns
        else:
            return context_in, None

    def forward(self, queries, keys, values, attn_mask, tau=None, delta=None):
        # queries: [B, L_Q, H, D]
        # keys:    [B, L_K, H, D]
        # values:  [B, L_V, H, D]  (L_K == L_V)
        B, L_Q, H, D = queries.shape
        _, L_K, _, _ = keys.shape

        # ProbAttention 的内部计算期望的维度是 [B, H, L, D]，所以先进行转置。
        queries = queries.transpose(1, 2)
        keys = keys.transpose(1, 2)
        values = values.transpose(1, 2)

        # 计算采样和top-k的数量
        # U_part 是每个查询需要采样的键的数量
        U_part = self.factor * np.ceil(np.log(L_K)).astype('int').item()
        # u 是需要选出的top-k查询的数量
        u = self.factor * np.ceil(np.log(L_Q)).astype('int').item()

        # 确保采样数和top-k数不超过序列长度
        U_part = U_part if U_part < L_K else L_K
        u = u if u < L_Q else L_Q

        # 核心步骤：获取稀疏的 QK 矩阵和重要查询的索引
        scores_top, index = self._prob_QK(queries, keys, sample_k=U_part, n_top=u)

        # 添加缩放因子
        scale = self.scale or 1. / sqrt(D)
        if scale is not None:
            scores_top = scores_top * scale
            
        # 获取初始上下文
        context = self._get_initial_context(values, L_Q)
        # 更新重要查询的上下文
        context, attn = self._update_context(context, values, scores_top, index, L_Q, attn_mask)

        # 将维度转置回来以匹配标准输出格式 [B, L, H*D]
        return context.transpose(1, 2).contiguous(), attn


class AttentionLayer(nn.Module):
    """
    一个通用的注意力"包装"层或"管理"层。
    它负责处理输入、生成Q,K,V、调用内部的注意力计算模块(如ProbAttention)、并处理最终输出。
    这样做可以将注意力计算的核心逻辑与Q,K,V的生成解耦。
    """
    def __init__(self, attention, d_model, n_heads, d_keys=None,
                 d_values=None):
        super(AttentionLayer, self).__init__()

        # 如果没有指定 d_keys 或 d_values，则默认为 d_model // n_heads
        d_keys = d_keys or (d_model // n_heads)
        d_values = d_values or (d_model // n_heads)

        # 内部的注意力计算模块，可以是 FullAttention, ProbAttention 等。
        self.inner_attention = attention
        # 用于生成 Q, K, V 的三个独立的线性投影层。
        self.query_projection = nn.Linear(d_model, d_keys * n_heads)
        self.key_projection = nn.Linear(d_model, d_keys * n_heads)
        self.value_projection = nn.Linear(d_model, d_values * n_heads)
        # 用于将多头注意力的输出拼接并投影回 d_model 维度的最终输出层。
        self.out_projection = nn.Linear(d_values * n_heads, d_model)
        self.n_heads = n_heads

    def forward(self, queries, keys, values, attn_mask, tau=None, delta=None):
        B, L, _ = queries.shape
        _, S, _ = keys.shape
        H = self.n_heads

        # 1. 将输入分别通过线性层，生成 Q, K, V。
        # 2. 使用 .view() 将 d_model 维度拆分为 n_heads 个头，方便并行计算。
        #    最终形状变为 [B, L, H, d_keys]
        queries = self.query_projection(queries).view(B, L, H, -1)
        keys = self.key_projection(keys).view(B, S, H, -1)
        values = self.value_projection(values).view(B, S, H, -1)

        # 3. 调用内部的注意力模块进行核心计算。
        out, attn = self.inner_attention(
            queries,
            keys,
            values,
            attn_mask,
            tau=tau,
            delta=delta
        )
        # 4. 将多头输出重新拼接起来。-1会自动计算为 H * d_values。
        out = out.view(B, L, -1)
        
        # 5. 通过最终的线性层，将维度投影回 d_model。
        return self.out_projection(out), attn


# --- 以下是其他模型中使用的注意力机制，非Informer核心 ---

class ReformerLayer(nn.Module):
    """
    Reformer 中使用的 LSH (Locality-Sensitive Hashing) 注意力。
    它通过哈希将相似的查询和键分到同一个桶(bucket)中，只在桶内进行注意力计算，
    从而在理论上将复杂度降低到 O(L log L)。
    """
    def __init__(self, attention, d_model, n_heads, d_keys=None,
                 d_values=None, causal=False, bucket_size=4, n_hashes=4):
        super().__init__()
        self.bucket_size = bucket_size
        self.attn = LSHSelfAttention(
            dim=d_model,
            heads=n_heads,
            bucket_size=bucket_size,
            n_hashes=n_hashes,
            causal=causal
        )

    def fit_length(self, queries):
        # Reformer 的 LSH 注意力要求序列长度必须是 bucket_size * 2 的整数倍。
        # 这个函数用来对序列进行填充以满足要求。
        B, N, C = queries.shape
        if N % (self.bucket_size * 2) == 0:
            return queries
        else:
            fill_len = (self.bucket_size * 2) - (N % (self.bucket_size * 2))
            return torch.cat([queries, torch.zeros([B, fill_len, C]).to(queries.device)], dim=1)

    def forward(self, queries, keys, values, attn_mask, tau, delta):
        # LSHSelfAttention 默认 Q=K=V
        B, N, C = queries.shape
        # 先填充长度，再计算注意力，最后裁剪回原始长度。
        queries = self.attn(self.fit_length(queries))[:, :N, :]
        return queries, None


class TwoStageAttentionLayer(nn.Module):
    """
    Pyraformer 中使用的两阶段注意力机制。
    它将注意力分解为 Intra-Scale (时间步内部) 和 Inter-Scale (跨尺度) 两个阶段。
    """
    def __init__(self, configs,
                 seg_num, factor, d_model, n_heads, d_ff=None, dropout=0.1):
        super(TwoStageAttentionLayer, self).__init__()
        d_ff = d_ff or 4 * d_model
        # 时间注意力
        self.time_attention = AttentionLayer(FullAttention(False, configs.factor, attention_dropout=configs.dropout,
                                                           output_attention=configs.output_attention), d_model, n_heads)
        # 维度注意力
        self.dim_sender = AttentionLayer(FullAttention(False, configs.factor, attention_dropout=configs.dropout,
                                                       output_attention=configs.output_attention), d_model, n_heads)
        self.dim_receiver = AttentionLayer(FullAttention(False, configs.factor, attention_dropout=configs.dropout,
                                                         output_attention=configs.output_attention), d_model, n_heads)
        self.router = nn.Parameter(torch.randn(seg_num, factor, d_model))

        self.dropout = nn.Dropout(dropout)

        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model)
        self.norm4 = nn.LayerNorm(d_model)

        self.MLP1 = nn.Sequential(nn.Linear(d_model, d_ff),
                                  nn.GELU(),
                                  nn.Linear(d_ff, d_model))
        self.MLP2 = nn.Sequential(nn.Linear(d_model, d_ff),
                                  nn.GELU(),
                                  nn.Linear(d_ff, d_model))

    def forward(self, x, attn_mask=None, tau=None, delta=None):
        # 跨时间步阶段 (Cross Time Stage)
        batch = x.shape[0]
        time_in = rearrange(x, 'b ts_d seg_num d_model -> (b ts_d) seg_num d_model')
        time_enc, attn = self.time_attention(
            time_in, time_in, time_in, attn_mask=None, tau=None, delta=None
        )
        dim_in = time_in + self.dropout(time_enc)
        dim_in = self.norm1(dim_in)
        dim_in = dim_in + self.dropout(self.MLP1(dim_in))
        dim_in = self.norm2(dim_in)

        # 跨维度阶段 (Cross Dimension Stage)
        dim_send = rearrange(dim_in, '(b ts_d) seg_num d_model -> (b seg_num) ts_d d_model', b=batch)
        batch_router = repeat(self.router, 'seg_num factor d_model -> (repeat seg_num) factor d_model', repeat=batch)
        dim_buffer, attn = self.dim_sender(batch_router, dim_send, dim_send, attn_mask=None, tau=None, delta=None)
        dim_receive, attn = self.dim_receiver(dim_send, dim_buffer, dim_buffer, attn_mask=None, tau=None, delta=None)
        dim_enc = dim_send + self.dropout(dim_receive)
        dim_enc = self.norm3(dim_enc)
        dim_enc = dim_enc + self.dropout(self.MLP2(dim_enc))
        dim_enc = self.norm4(dim_enc)

        final_out = rearrange(dim_enc, '(b seg_num) ts_d d_model -> b ts_d seg_num d_model', b=batch)

        return final_out
