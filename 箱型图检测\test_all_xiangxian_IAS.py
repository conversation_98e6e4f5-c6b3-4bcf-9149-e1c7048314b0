import pandas as pd
import torch
from sklearn.preprocessing import MinMaxScaler
import os
import sys
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import json
from sklearn.neighbors import LocalOutlierFactor

# --- 新增：动态将项目根目录添加到Python路径 ---
# 获取当前脚本的绝对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录 (假设此脚本位于根目录下的 'LOF方法检测' 文件夹中)
project_root = os.path.dirname(current_dir)
# 将项目根目录添加到sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)


# --- 1. 修改: 从我们现有的 "多对一" 脚本中导入配置 ---
from myinformer_IAS import TrainingConfig, Namespace
from models import Informer
from utils.timefeatures import time_features


# 解决Matplotlib画图中文显示问题
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use("ggplot")


def load_and_process_segmented_data_for_test(file_path, config, scaler, feature_cols):
    """
    一个专门为 "多对一" 测试脚本修改的数据处理函数。
    """
    print(f"--- 开始处理测试文件: {file_path} ---")
    df = pd.read_csv(file_path)

    # --- 新增: 检查并加载真实异常标签 ---
    if 'is_anomaly' in df.columns:
        print("发现 'is_anomaly' 列，将用作异常检测评估的真实标签。")
        anomaly_labels = df['is_anomaly'].values
    else:
        print("未发现 'is_anomaly' 列，将假设所有点均为正常点。")
        anomaly_labels = np.zeros(len(df))

    df['datetime'] = pd.to_datetime(df[config.time_column], format='%H:%M:%S')

    df_stamp = df[['datetime']].copy()
    df_stamp.rename(columns={'datetime': 'date'}, inplace=True)
    time_marks = time_features(df_stamp, timeenc=1, freq=config.freq)

    df_features = df[feature_cols]
    scaled_features = scaler.transform(df_features)
    
    # --- 2. 修改: 增加获取目标列索引的逻辑 ---
    try:
        target_col_index = feature_cols.index(config.target_feature)
    except ValueError:
        raise ValueError(f"错误: 目标特征 '{config.target_feature}' 不在特征列表 feature_cols 中。")

    print("正在根据 '起点' 和 '终点' 标记识别飞行段...")
    segments = []
    segment_start_index = -1
    for i, row in df.iterrows():
        marker = str(row[config.segment_marker_column])
        if '起点' in marker:
            segment_start_index = i
        elif '终点' in marker and segment_start_index != -1:
            segment_df = df.loc[segment_start_index:i].copy()
            segments.append(segment_df)
            segment_start_index = -1
            
    print(f"在文件 {file_path} 中识别出 {len(segments)} 个有效飞行段。")
    
    all_x, all_y, all_x_mark, all_y_mark = [], [], [], []
    segment_window_counts = [] 
    all_target_times = []
    initial_segments_for_plot = [] 
    all_ground_truth = [] # <--- 新增

    for segment_df in segments:
        if len(segment_df) >= config.seq_len:
            initial_data_dict = {
                'values': segment_df.iloc[:config.seq_len][config.target_feature].tolist(),
                'times': segment_df.iloc[:config.seq_len]['datetime'].tolist()
            }
            initial_segments_for_plot.append(initial_data_dict)
        else:
            initial_segments_for_plot.append(None)

        segment_indices = segment_df.index
        current_segment_features = scaled_features[segment_indices]
        current_segment_marks = time_marks[segment_indices]
        
        total_sequence_length = config.seq_len + config.pred_len
        
        if len(current_segment_features) < total_sequence_length:
            segment_window_counts.append(0)
            continue

        num_windows_in_segment = 0
        for i in range(len(current_segment_features) - total_sequence_length + 1):
            x_seq = current_segment_features[i : i + config.seq_len]
            x_mark_seq = current_segment_marks[i : i + config.seq_len]
            y_start_index = i + config.seq_len - config.label_len
            y_end_index = i + total_sequence_length
            
            # --- 3. 修改: y_seq只提取目标特征那一列 ---
            y_seq_full = current_segment_features[y_start_index : y_end_index]
            y_seq_target = y_seq_full[:, target_col_index:target_col_index+1]
            
            y_mark_seq = current_segment_marks[y_start_index : y_end_index]

            all_x.append(x_seq)
            all_x_mark.append(x_mark_seq)
            all_y.append(y_seq_target) # <--- 使用只包含目标特征的y
            all_y_mark.append(y_mark_seq)
            
            target_original_index = segment_df.index[i + total_sequence_length - 1]
            all_target_times.append(df.loc[target_original_index, 'datetime'])
            
            # --- 新增: 收集真实标签 ---
            all_ground_truth.append(anomaly_labels[target_original_index])
            
            num_windows_in_segment += 1
        
        segment_window_counts.append(num_windows_in_segment)
            
    total_windows = len(all_x)
    print(f"文件 {file_path} 处理完毕，共生成 {total_windows} 个有效窗口。")

    final_x = torch.tensor(np.array(all_x), dtype=torch.float32)
    final_y = torch.tensor(np.array(all_y), dtype=torch.float32)
    final_x_mark = torch.tensor(np.array(all_x_mark), dtype=torch.float32)
    final_y_mark = torch.tensor(np.array(all_y_mark), dtype=torch.float32)
    final_ground_truth = torch.tensor(np.array(all_ground_truth), dtype=torch.long)
    
    return final_x, final_y, final_x_mark, final_y_mark, segment_window_counts, all_target_times, initial_segments_for_plot, final_ground_truth


def test_and_plot_predictions(model, loader, scaler, config, device, output_dir, feature_cols, segment_lengths=None, target_times=None, initial_segments_for_plot=None):
    """
    --- 4. 重构: 整个函数被重构以适应单变量预测和评估 ---
    """
    print(f"\n--- 开始在测试集上评估和预测唯一目标: {config.target_feature} ---")
    model.eval()
    all_preds, all_trues, all_ground_truth = [], [], []
    with torch.no_grad():
        for i, (batch_x, batch_y, batch_x_mark, batch_y_mark, batch_ground_truth) in enumerate(loader):
            batch_x, batch_y, batch_x_mark, batch_y_mark = \
                batch_x.to(device), batch_y.to(device), batch_x_mark.to(device), batch_y_mark.to(device)
            
            dec_inp = torch.zeros_like(batch_y[:, -config.pred_len:, :]).float()
            dec_inp = torch.cat([batch_y[:, :config.label_len, :], dec_inp], dim=1).float().to(device)
            
            outputs = model(batch_x, batch_x_mark, dec_inp, batch_y_mark)
            
            true_targets = batch_y[:, -config.pred_len:, :]
            pred_targets = outputs
            
            all_preds.append(pred_targets.detach().cpu().numpy())
            all_trues.append(true_targets.detach().cpu().numpy())
            all_ground_truth.append(batch_ground_truth.cpu().numpy())

    preds = np.concatenate(all_preds, axis=0)
    trues = np.concatenate(all_trues, axis=0)
    ground_truth_labels = np.concatenate(all_ground_truth, axis=0).flatten()

    if preds.ndim == 3:
        preds = preds[:, -1, :]
        trues = trues[:, -1, :]
        
    # --- 5. 修改: 使用 "哑数组" 方法进行单变量反归一化 ---
    num_features = len(feature_cols)
    target_col_index = feature_cols.index(config.target_feature)
    
    dummy_preds = np.zeros((preds.shape[0], num_features))
    dummy_trues = np.zeros((trues.shape[0], num_features))

    dummy_preds[:, target_col_index] = preds.squeeze()
    dummy_trues[:, target_col_index] = trues.squeeze()

    preds_unscaled_full = scaler.inverse_transform(dummy_preds)
    trues_unscaled_full = scaler.inverse_transform(dummy_trues)

    feature_preds = preds_unscaled_full[:, target_col_index]
    feature_trues = trues_unscaled_full[:, target_col_index]

    # --- 新增: 异常检测模块 (IQR 方法) ---
    print("\n--- 正在使用 IQR 方法进行异常检测 ---")
    errors = np.abs(feature_trues - feature_preds)
    
    Q1 = np.percentile(errors, 25)
    Q3 = np.percentile(errors, 75)
    IQR = Q3 - Q1
    feature_threshold = Q3 + 1.5 * IQR # 使用3.0作为更严格的系数
    print(f"动态计算出特征 '{config.target_feature}' 的异常阈值: > {feature_threshold:.4f} (基于 Q1={Q1:.4f}, Q3={Q3:.4f}, IQR={IQR:.4f})")

    # --- 新增: 计算 TP, FP, TN, FN ---
    predicted_anomalies = (errors > feature_threshold).astype(int)
    
    TP = np.sum((predicted_anomalies == 1) & (ground_truth_labels == 1))
    FP = np.sum((predicted_anomalies == 1) & (ground_truth_labels == 0))
    TN = np.sum((predicted_anomalies == 0) & (ground_truth_labels == 0))
    FN = np.sum((predicted_anomalies == 0) & (ground_truth_labels == 1))
    
    # --- 新增: 计算评估指标 ---
    TPR = TP / (TP + FN) if (TP + FN) > 0 else 0  # 检测率 (Recall)
    FPR = FP / (FP + TN) if (FP + TN) > 0 else 0  # 误检率
    Pre = TP / (TP + FP) if (TP + FP) > 0 else 0  # 精确度
    Acc = (TP + TN) / (TP + TN + FP + FN) if (TP + TN + FP + FN) > 0 else 0 # 准确率
    F1 = 2 * Pre * TPR / (Pre + TPR) if (Pre + TPR) > 0 else 0 # F1分数
    
    print("\n--- 异常检测评估指标 ---")
    print(f"TP: {TP}, FP: {FP}, TN: {TN}, FN: {FN}")
    print(f"检测率 (TPR/Recall): {TPR:.4f}")
    print(f"误检率 (FPR): {FPR:.4f}")
    print(f"精确度 (Precision): {Pre:.4f}")
    print(f"准确率 (Accuracy): {Acc:.4f}")
    print(f"F1 分数: {F1:.4f}")
    
    anomaly_metrics = {
        'TP': TP, 'FP': FP, 'TN': TN, 'FN': FN,
        'TPR': TPR, 'FPR': FPR, 'Precision': Pre,
        'Accuracy': Acc, 'F1_Score': F1,
        'Threshold': feature_threshold
    }
    
    # --- 保存异常检测指标到单独的CSV
    anomaly_metrics_df = pd.DataFrame([anomaly_metrics])
    anomaly_metrics_save_path = os.path.join(output_dir, 'test_anomaly_evaluation_metrics.csv')
    anomaly_metrics_df.to_csv(anomaly_metrics_save_path, index=False, encoding='utf-8-sig')
    print(f"异常检测评估指标已保存到: {anomaly_metrics_save_path}")

    all_anomalies_report = []
    
    # --- 6. 简化: 计算唯一的评估指标 ---
    r2 = r2_score(feature_trues, feature_preds)
    mse = mean_squared_error(feature_trues, feature_preds)
    mae = mean_absolute_error(feature_trues, feature_preds)
    print(f"测试集评估结果 ({config.target_feature}) - R2: {r2:.4f}, MSE: {mse:.4f}, MAE: {mae:.4f}")
    
    metrics_data = [{'Feature': config.target_feature, 'R2': r2, 'MSE': mse, 'MAE': mae}]
    
    # --- 7. 简化: 绘制唯一的预测结果图 ---
    valid_segments_to_plot = [l for l in segment_lengths if l > 0]
    n_segments = len(valid_segments_to_plot)

    if n_segments > 0:
        if n_segments == 1: n_cols = 1; figsize = (12, 5)
        else: n_cols = 2; figsize = (15, ((n_segments + 1) // 2) * 4)
        n_rows = (n_segments + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize, squeeze=False)
        fig.suptitle(f'TEST SET: Segment-wise Prediction vs Real ({config.target_feature})', fontsize=16)
        
        current_idx = 0
        plot_idx = 0
        for seg_idx, length in enumerate(segment_lengths):
            if length == 0: continue
            
            initial_plot_data = initial_segments_for_plot[seg_idx]
            
            segment_trues = feature_trues[current_idx : current_idx + length]
            segment_preds = feature_preds[current_idx : current_idx + length]
            segment_times = target_times[current_idx : current_idx + length]
            
            row, col = plot_idx // n_cols, plot_idx % n_cols
            ax = axes[row, col]

            # 绘制历史和真实值
            if initial_plot_data:
                full_real_times = initial_plot_data['times'] + segment_times
                full_real_values = initial_plot_data['values'] + list(segment_trues)
                ax.plot(full_real_times, full_real_values, label='Real Value (含历史)', color='#ff7f0e')
                ax.axvline(x=initial_plot_data['times'][-1], color='red', linestyle='--', linewidth=1.5, label='预测起点')
            else:
                 ax.plot(segment_times, segment_trues, label='Real Value', color='#ff7f0e')

            # 绘制预测值
            ax.plot(segment_times, segment_preds, label='Predicted Value', color='#1f77b4', linewidth=2)
            
            # --- 新增: 在图上标记异常点 ---
            segment_errors = errors[current_idx : current_idx + length]
            anomaly_indices_in_segment = np.where(segment_errors > feature_threshold)[0]
            
            if len(anomaly_indices_in_segment) > 0:
                anomaly_times = [segment_times[j] for j in anomaly_indices_in_segment]
                anomaly_values = segment_trues[anomaly_indices_in_segment]
                ax.scatter(anomaly_times, anomaly_values, color='red', s=50, zorder=5, label='异常点')
                
                # 记录异常信息到报告
                for anom_idx in anomaly_indices_in_segment:
                    all_anomalies_report.append({
                        'Time': segment_times[anom_idx].strftime('%H:%M:%S'),
                        'Feature': config.target_feature,
                        'RealValue': segment_trues[anom_idx],
                        'PredictedValue': segment_preds[anom_idx],
                        'AnomalyScore': segment_errors[anom_idx],
                        'Threshold': feature_threshold
                    })

            ax.set_title(f"Test Segment {seg_idx+1}")
            ax.set_ylabel(config.target_feature)
            # 确保图例只显示一次，避免重复
            handles, labels = ax.get_legend_handles_labels()
            by_label = dict(zip(labels, handles))
            ax.legend(by_label.values(), by_label.keys())
            ax.grid(True)
            
            import matplotlib.dates as mdates
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
            plt.setp(ax.get_xticklabels(), rotation=30, ha="right")

            current_idx += length
            plot_idx += 1

        for j in range(plot_idx, n_rows * n_cols): fig.delaxes(axes.flatten()[j])
        plt.tight_layout(rect=[0, 0.03, 1, 0.95])
        save_path_img = os.path.join(output_dir, f'test_prediction_plot_{config.target_feature}.png')
        plt.savefig(save_path_img)
        print(f"预测结果图已保存到: {save_path_img}")
        plt.close(fig)

    # --- 新增：为误差生成箱线图 ---
    fig_box, ax_box = plt.subplots(figsize=(8, 6))
    ax_box.boxplot(errors)
    ax_box.set_title(f'预测误差分布箱线图 - {config.target_feature}')
    ax_box.set_ylabel('Prediction Error')
    ax_box.grid(True)
    ax_box.axhline(y=feature_threshold, color='r', linestyle='--', label=f'IQR 异常阈值: {feature_threshold:.4f}')
    ax_box.legend()
    save_path_box = os.path.join(output_dir, f'test_error_boxplot_{config.target_feature}.png')
    plt.savefig(save_path_box)
    print(f"特征'{config.target_feature}'的误差分布箱线图已保存。")
    plt.close(fig_box)

    # --- 8. 简化: 保存唯一的评估和预测结果 ---
    metrics_df = pd.DataFrame(metrics_data)
    metrics_save_path = os.path.join(output_dir, 'test_evaluation_metrics.csv')
    metrics_df.to_csv(metrics_save_path, index=False, encoding='utf-8-sig')
    print(f"\n评估指标已保存到: {metrics_save_path}")

    prediction_dfs = {'Time': [t.strftime('%H:%M:%S') for t in target_times],
                      f'Real_{config.target_feature}': feature_trues,
                      f'Predicted_{config.target_feature}': feature_preds}
    results_df = pd.DataFrame(prediction_dfs)
    results_save_path = os.path.join(output_dir, 'test_predictions.csv')
    results_df.to_csv(results_save_path, index=False, encoding='utf-8-sig')
    print(f"预测结果CSV已保存到: {results_save_path}")

    # --- 新增: 保存异常检测报告CSV ---
    if all_anomalies_report:
        anomalies_df = pd.DataFrame(all_anomalies_report)
        anomalies_df.sort_values(by=['Time', 'Feature'], inplace=True)
        anomalies_save_path = os.path.join(output_dir, 'test_anomalies_report.csv')
        anomalies_df.to_csv(anomalies_save_path, index=False, encoding='utf-8-sig')
        print(f"异常检测报告已保存到: {anomalies_save_path}")
    else:
        print("在本次测试中未检测到异常点。")

if __name__ == '__main__':
    # --- 9. 修改: 主函数逻辑全面适配 "多对一" 测试 ---
    config = TrainingConfig() # 使用 myinformer_.py 中的配置
    
    test_file_path = 'data/爬升/48_IAS.csv'
    
    # 结果将保存在 "多对一" 模型训练结果目录下的一个 'test_results' 子文件夹中
    model_dir = config.results_dir
    test_results_dir = os.path.join(model_dir, 'test_results_single_target')
    os.makedirs(test_results_dir, exist_ok=True)
    print(f"测试结果将保存到: '{test_results_dir}'")
    
    # 请确保这个路径指向你训练好的 "多对一" 模型
    model_path = os.path.join(model_dir, f'best_model_{config.target_feature}.pth')
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"未找到单变量模型, 请确认路径正确: {model_path}")
    if not os.path.exists(test_file_path):
         raise FileNotFoundError(f"未找到测试集文件: {test_file_path}")

    # --- 加载训练集以拟合缩放器并获取特征列表 ---
    print("\n--- 在原始训练集上重新拟合缩放器和确定特征 ---")
    df_train_raw = pd.read_csv(config.train_file_path)
    
    # --- <修改> 直接使用训练配置中定义的特征列表 ---
    feature_cols = config.use_features
    
    # 安全检查：确保所有指定的特征都存在于DataFrame中
    missing_in_df = [col for col in feature_cols if col not in df_train_raw.columns]
    if missing_in_df:
        raise ValueError(f"致命错误: 在训练数据中找不到以下指定的特征: {missing_in_df}")

    # 安全检查：确保目标特征在所选特征列表中
    if config.target_feature not in feature_cols:
        raise ValueError(f"致命错误: 目标特征 '{config.target_feature}' 不在指定的 use_features 列表中。")

    scaler = MinMaxScaler()
    scaler.fit(df_train_raw[feature_cols])
    print(f"缩放器拟合成功，将使用以下 {len(feature_cols)} 个特征作为输入: {feature_cols}")
    print(f"测试目标为: ['{config.target_feature}']")

    # --- 10. 修改: 调整代码顺序，先计算 target_dim_index，再创建模型 ---
    # 确保配置与模型匹配
    config.data_dim = len(feature_cols)
    try:
        config.target_dim_index = feature_cols.index(config.target_feature)
        print(f"目标特征 '{config.target_feature}' 在输入特征中的索引位置是: {config.target_dim_index}")
    except ValueError:
        raise ValueError(f"致命错误: 目标特征 '{config.target_feature}' 没有在最终的特征列表 feature_cols 中找到。")

    # --- 加载已训练的模型 ---
    print("\n--- 加载已训练的 '多对一' 模型 ---")
    informer_config_obj = config.to_informer_config()
    model = Informer.Model(informer_config_obj).to(device)
    
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.eval() 
    print(f"模型已从 {model_path} 加载")

    # --- 加载并处理测试数据 ---
    print("\n--- 处理测试数据 ---")
    test_x, test_y, test_x_mark, test_y_mark, test_segment_lengths, test_target_times, initial_segments_for_plot, test_ground_truth = \
        load_and_process_segmented_data_for_test(test_file_path, config, scaler, feature_cols)

    test_dataset = TensorDataset(test_x, test_y, test_x_mark, test_y_mark, test_ground_truth)
    test_loader = DataLoader(test_dataset, batch_size=config.batch_size, shuffle=False)
    print("测试数据加载完毕。")

    # --- 在测试集上评估并可视化结果 ---
    test_and_plot_predictions(
        model=model,
        loader=test_loader,
        scaler=scaler,
        config=config,
        device=device,
        output_dir=test_results_dir,
        feature_cols=feature_cols,
        segment_lengths=test_segment_lengths,
        target_times=test_target_times,
        initial_segments_for_plot=initial_segments_for_plot
    ) 