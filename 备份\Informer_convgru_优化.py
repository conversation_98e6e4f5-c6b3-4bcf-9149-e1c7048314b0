import torch
import torch.nn as nn
import torch.nn.functional as F

# 从自定义的 layers 目录中导入所需的模块。这些模块是构成 Informer 模型的基本组件。
# Decoder/Encoder: 解码器/编码器的整体框架
# DecoderLayer/EncoderLayer:构成解码器/编码器的单层结构
# ConvLayer: Informer 特有的"蒸馏"操作中使用的卷积层
from layers.Transformer_EncDec import Decoder, DecoderLayer, Encoder, EncoderLayer, ConvLayer
# ProbAttention: Informer 论文提出的核心，ProbSparse 自注意力机制
# AttentionLayer: 一个包装层，用于方便地生成Q,K,V并执行注意力计算
from layers.SelfAttention_Family import ProbAttention, AttentionLayer
# DataEmbedding: 将原始输入数据（包括时间戳特征）打包并嵌入到高维空间的模块
from layers.Embed import DataEmbedding
# 导入 ConvGRU 模块
from .convgru.convgru import ConvGRU


class Model(nn.Module):
    """
    Informer 模型主类
    该模型实现了 Informer 论文中提出的具有 O(L log L) 复杂度的 ProbSparse 自注意力机制。
    论文链接: https://ojs.aaai.org/index.php/AAAI/article/view/17325/17132
    """

    def __init__(self, configs):
        """
        模型的构造函数，负责初始化所有的网络层和参数。
        这个函数只定义了模型的"结构蓝图"，并没有实际的数据计算。
        参数:
            configs (object): 一个包含所有模型超参数的配置对象。这个对象通常由一个参数解析器从命令行或配置文件中生成。
        """
        super(Model, self).__init__()
        
        # --- 任务相关参数 ---
        self.configs = configs # <--- 新增: 保存整个配置对象
        # configs.task_name: 任务名称，如 'long_term_forecast' (长期预测), 'imputation' (插补) 等。
        self.task_name = configs.task_name
        # configs.pred_len: 预测序列的长度。模型需要一次性输出的未来时间步数。
        self.pred_len = configs.pred_len
        # configs.label_len: 在解码器中用作"引子"的真实标签序列的长度。
        self.label_len = configs.label_len

        # --- 新增: ConvGRU 开关和模块初始化 ---
        # 检查配置中是否有 use_convgru 参数，默认为 False
        self.use_convgru = getattr(configs, 'use_convgru', False)
        if self.use_convgru:
            print("INFO: ConvGRU module is enabled.")
            # 从配置中获取 ConvGRU 的参数
            convgru_hidden_sizes = getattr(configs, 'convgru_hidden_sizes', 512)
            convgru_kernel_sizes = getattr(configs, 'convgru_kernel_sizes', 3)
            convgru_n_layers = getattr(configs, 'convgru_n_layers', 1)
            
            # 初始化 ConvGRU 模块
            self.conv_gru = ConvGRU(
                input_size=configs.d_model,
                hidden_sizes=[convgru_hidden_sizes] * convgru_n_layers,
                kernel_sizes=[convgru_kernel_sizes] * convgru_n_layers,
                n_layers=convgru_n_layers
            )

        # --- 嵌入层 (Embedding) ---
        # 为编码器(Encoder)和解码器(Decoder)分别定义数据嵌入层。
        # 它们负责将输入的原始序列和时间戳特征转换为d_model维度的向量。
        # configs.enc_in: 编码器输入特征的维度 (c_in)。
        # configs.dec_in: 解码器输入特征的维度 (c_out)。
        # configs.d_model: 模型的主维度，所有 Transformer 层内部处理的向量维度。
        # configs.embed: 嵌入方式，这里是 'timeF'，表示使用时间特征嵌入。
        # configs.freq: 时间特征的频率，如 'h' (小时), 'd' (天)。
        # configs.dropout: Dropout 概率。
        self.enc_embedding = DataEmbedding(configs.enc_in, configs.d_model, configs.embed, configs.freq,
                                           configs.dropout)
        self.dec_embedding = DataEmbedding(configs.dec_in, configs.d_model, configs.embed, configs.freq,
                                           configs.dropout)

        # --- 编码器 (Encoder) ---
        # Informer 的编码器由多个 EncoderLayer 堆叠而成。
        self.encoder = Encoder(
            # 1. EncoderLayer 列表:
            #    这是一个列表推导式，根据 configs.e_layers (编码器层数) 创建多个 EncoderLayer。
            [
                #调用 EncoderLayer 类的构造函数 __init__
                EncoderLayer(
                    # 1.1 AttentionLayer: 每个 EncoderLayer 都包含一个注意力层。调用 AttentionLayer 类的构造函数 __init__
                    AttentionLayer(
                        # 1.1.1 ProbAttention: 注意力层的核心是 ProbSparse 自注意力机制。调用 ProbAttention 类的构造函数 __init__
                        #         - False: 表示在编码器中不使用下三角掩码(mask)，因为编码器可以"看到"整个输入序列。
                        #         - configs.factor: ProbSparse 注意力中的采样因子。
                        #         - configs.dropout: 注意力权重矩阵的 Dropout。
                        #         - configs.output_attention: 是否输出注意力权重矩阵。
                        ProbAttention(False, configs.factor, attention_dropout=configs.dropout,
                                      output_attention=configs.output_attention),
                        # 1.1.2 AttentionLayer 的参数:
                        configs.d_model, configs.n_heads), # 传入模型主维度和头数
                    
                    # 1.2 EncoderLayer 的其他参数:
                    configs.d_model, # 模型主维度
                    configs.d_ff,    # 前馈网络(FFN)的隐藏层维度
                    dropout=configs.dropout,
                    activation=configs.activation
                ) for l in range(configs.e_layers)
            ],
            # 2. 卷积蒸馏层 (ConvLayer) 列表:
            #    这是 Informer 的一个创新点，在每个 EncoderLayer 后接一个卷积层来"蒸馏"序列，缩短长度。
            #    注意，列表长度是 e_layers - 1，因为最后一层编码器后不进行蒸馏。
            [
                ConvLayer(
                    configs.d_model
                ) for l in range(configs.e_layers - 1)
            # 这个蒸馏操作只在需要时 (configs.distil=True) 且任务是预测时 ('forecast' in task_name) 才被创建。
            ] if configs.distil and ('forecast' in configs.task_name) else None,
            
            # 3. 归一化层 (Normalization Layer):
            #    在所有 EncoderLayer 执行完毕后，对最终输出进行层归一化。
            norm_layer=torch.nn.LayerNorm(configs.d_model)
        )
        
        # --- 解码器 (Decoder) ---
        # Informer 的解码器由多个 DecoderLayer 堆叠而成。
        self.decoder = Decoder(
            # 1. DecoderLayer 列表:
            #    根据 configs.d_layers (解码器层数) 创建多个 DecoderLayer。
            [
                DecoderLayer(
                    # 1.1 第一个 AttentionLayer (自注意力):
                    #     用于处理解码器自身的输入序列。
                    AttentionLayer(
                        # 1.1.1 ProbAttention (带掩码):
                        #         - True: 表示在解码器自注意力中必须使用下三角掩码，防止未来的信息泄露。
                        ProbAttention(True, configs.factor, attention_dropout=configs.dropout, output_attention=False),
                        configs.d_model, configs.n_heads),
                    
                    # 1.2 第二个 AttentionLayer (交叉注意力):
                    #     用于处理编码器的输出和解码器的输入，这是编码器-解码器架构的核心。
                    AttentionLayer(
                        # 1.2.1 ProbAttention (不带掩码):
                        #         - False: 交叉注意力中，查询(Q)来自解码器，键(K)和值(V)来自编码器，不需要掩码。
                        ProbAttention(False, configs.factor, attention_dropout=configs.dropout, output_attention=False),
                        configs.d_model, configs.n_heads),
                    
                    # 1.3 DecoderLayer 的其他参数:
                    configs.d_model,
                    configs.d_ff,
                    dropout=configs.dropout,
                    activation=configs.activation,
                )
                for l in range(configs.d_layers)
            ],
            # 2. 归一化层:
            #    在所有 DecoderLayer 执行完毕后，对最终输出进行层归一化。
            norm_layer=torch.nn.LayerNorm(configs.d_model),
            # 3. 最终投影层:
            #    一个全连接层，将解码器输出的 d_model 维度向量映射回任务所需的输出维度 (configs.c_out)。
            projection=nn.Linear(configs.d_model, configs.c_out, bias=True)
        )
        
        # --- 针对特定任务的投影层 ---
        # 根据不同的任务，可能需要一个额外的、不同的最终投影层。
        if self.task_name == 'imputation':
            self.projection = nn.Linear(configs.d_model, configs.c_out, bias=True)
        if self.task_name == 'anomaly_detection':
            self.projection = nn.Linear(configs.d_model, configs.c_out, bias=True)
        if self.task_name == 'classification':
            self.act = F.gelu # 激活函数
            self.dropout = nn.Dropout(configs.dropout)
            # 分类任务的投影层输入是整个序列的 d_model 向量展平后的结果。
            self.projection = nn.Linear(configs.d_model * configs.seq_len, configs.num_class)

    def long_forecast(self, x_enc, x_mark_enc, x_dec, x_mark_dec):
        """长期预测任务的前向传播逻辑"""
        # 1. 将编码器输入和解码器输入分别送入嵌入层。
        enc_out = self.enc_embedding(x_enc, x_mark_enc)
        dec_out = self.dec_embedding(x_dec, x_mark_dec)

        # 2. 编码过程: 将嵌入后的编码器输入送入编码器，得到编码后的高维表示(enc_out)和注意力权重(attns)。
        #    attn_mask=None 表示不使用额外的掩码（ProbAttention内部会自己处理）。
        enc_out, attns = self.encoder(enc_out, attn_mask=None)

        # 3. 解码过程: 将嵌入后的解码器输入(dec_out)和编码器的输出(enc_out)一同送入解码器。
        #    解码器内部会先进行自注意力，再进行交叉注意力。
        dec_out = self.decoder(dec_out, enc_out, x_mask=None, cross_mask=None)

        return dec_out  # 返回最终的预测结果，形状为 [批量, 预测长度, 特征维度]

    def short_forecast(self, x_enc, x_mark_enc, x_dec, x_mark_dec):
        """短期预测任务的前向传播逻辑 (带实例归一化)"""
        # --- 实例归一化 (Instance Normalization) ---
        # 这是 DLinear 等工作中发现的有效技巧，可以稳定训练过程。
        # 1. 计算每个样本的均值和标准差，用于归一化。
        mean_enc = x_enc.mean(1, keepdim=True).detach()  # B x 1 x E
        x_enc = x_enc - mean_enc
        std_enc = torch.sqrt(torch.var(x_enc, dim=1, keepdim=True, unbiased=False) + 1e-5).detach()  # B x 1 x E
        x_enc = x_enc / std_enc
        # --- 归一化结束 ---

        # 2. 执行与 long_forecast 相同的嵌入、编码、解码过程。
        enc_out = self.enc_embedding(x_enc, x_mark_enc)
        
        # --- <<< ConvGRU 模块集成点 >>> ---
        if self.use_convgru and hasattr(self, 'conv_gru'):
            # --- <<< 优化：引入残差连接 >>> ---
            # 1. 克隆一份原始的嵌入输出，用于后续的残差连接
            original_enc_out = enc_out.clone()
            
            b, l, d = enc_out.shape
            # ConvGRU 的隐藏状态在批次中是持久的，但在不同批次间重置
            hidden_state = None 
            outputs_gru = []
            
            # 循环遍历序列的每个时间步
            for t in range(l):
                # 获取当前时间步的输入 (B, D)，并整形为 (B, D, 1, 1) -> (32, 512, 1, 1) 以适配 Conv2d
                input_t = enc_out[:, t, :].unsqueeze(-1).unsqueeze(-1)
                
                # 将单个时间步的输入和前一个隐藏状态送入ConvGRU
                # self.conv_gru 的 forward 方法处理一个时间步的堆叠层
                hidden_state = self.conv_gru(input_t, hidden_state)
                
                # 收集最后一层 ConvGRU cell 的输出
                # hidden_state 是一个列表，包含每层的隐藏状态
                output_t = hidden_state[-1] # shape: (B, hidden, 1, 1)
                outputs_gru.append(output_t.squeeze(-1).squeeze(-1))
            
            # 2. 将所有时间步的输出堆叠起来，形成ConvGRU的输出
            convgru_output = torch.stack(outputs_gru, dim=1) # shape: (B, L, D)

            # 3. 将原始嵌入特征与ConvGRU处理后的特征相加 (残差连接)
            # 这允许模型同时利用高频细节和时序平滑特征
            enc_out = original_enc_out + convgru_output

        dec_out = self.dec_embedding(x_dec, x_mark_dec)
        enc_out, attns = self.encoder(enc_out, attn_mask=None)
        dec_out = self.decoder(dec_out, enc_out, x_mask=None, cross_mask=None)
        
        # 3. 反归一化: 将模型的输出乘以标准差再加上均值，恢复到原始的数据尺度。
        # --- <<< 修改: 仅使用目标维度的均值和标准差进行反归一化 >>> ---
        if self.task_name == 'short_term_forecast' and self.configs.c_out == 1:
            # 只取出目标维度的均值和标准差
            mean_target = mean_enc[:, :, self.configs.target_dim_index:self.configs.target_dim_index+1].detach()
            std_target = std_enc[:, :, self.configs.target_dim_index:self.configs.target_dim_index+1].detach()
            dec_out = dec_out * std_target + mean_target
        else:
            # 保持原有的多对多反归一化逻辑
            dec_out = dec_out * std_enc + mean_enc

        return dec_out

    def imputation(self, x_enc, x_mark_enc, x_dec, x_mark_dec, mask):
        """插补任务的前向传播逻辑"""
        # 1. 对输入进行嵌入。
        enc_out = self.enc_embedding(x_enc, x_mark_enc)
        # 2. 通过编码器。
        enc_out, attns = self.encoder(enc_out, attn_mask=None)
        # 3. 将编码器的输出直接通过投影层得到结果。插补任务不需要解码器。
        dec_out = self.projection(enc_out)
        return dec_out

    def anomaly_detection(self, x_enc):
        """异常检测任务的前向传播逻辑"""
        # 1. 嵌入输入。异常检测通常只使用数值特征，不使用时间戳特征 (x_mark_enc=None)。
        enc_out = self.enc_embedding(x_enc, None)
        # 2. 通过编码器。
        enc_out, attns = self.encoder(enc_out, attn_mask=None)
        # 3. 将编码器的输出直接通过投影层得到重构(reconstruction)结果。
        #    异常检测的逻辑是比较原始输入和重构结果的差异。
        dec_out = self.projection(enc_out)
        return dec_out

    def classification(self, x_enc, x_mark_enc):
        # enc
        enc_out = self.enc_embedding(x_enc, None)
        enc_out, attns = self.encoder(enc_out, attn_mask=None)

        # Output
        output = self.act(enc_out)
        output = self.dropout(output)
        output = output * x_enc.unsqueeze(-1)
        output = output.reshape(output.shape[0], -1)
        output = self.projection(output)
        return output

    def forward(self, x_enc, x_mark_enc, x_dec, x_mark_dec, mask=None):
        if self.task_name == 'long_term_forecast':
            dec_out = self.long_forecast(x_enc, x_mark_enc, x_dec, x_mark_dec)
            return dec_out[:, -self.pred_len:, :]  # [B, L, D]
        if self.task_name == 'short_term_forecast':
            dec_out = self.short_forecast(x_enc, x_mark_enc, x_dec, x_mark_dec)
            return dec_out[:, -self.pred_len:, :]  # [B, L, D]
        if self.task_name == 'imputation':
            dec_out = self.imputation(x_enc, x_mark_enc, x_dec, x_mark_dec, mask)
            return dec_out  # [B, L, D]
        if self.task_name == 'anomaly_detection':
            dec_out = self.anomaly_detection(x_enc)
            return dec_out  # [B, L, D]
        if self.task_name == 'classification':
            dec_out = self.classification(x_enc, x_mark_enc)
            return dec_out  # [B, N]
        return None
