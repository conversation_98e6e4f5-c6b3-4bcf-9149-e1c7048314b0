import pandas as pd
import os

file_path = 'data/起落航线/qiluo_筛选.csv'

# 检查文件是否存在
if not os.path.exists(file_path):
    print(f"错误：文件 '{file_path}' 不存在。")
else:
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path, dtype=str) # 将所有列都作为字符串读取以避免类型推断问题

        # 检查 'Time' 列是否存在
        if 'Time' in df.columns:
            # 去除 'Time' 列中每个值前面的空格
            # 使用 .str.lstrip() 来移除左侧的空格
            df['Time'] = df['Time'].str.lstrip()
            
            # 将修改后的DataFrame保存回CSV文件
            df.to_csv(file_path, index=False)
            
            print(f"文件 '{file_path}' 中的 'Time' 列已成功清理并保存。")
        else:
            print(f"错误：文件 '{file_path}' 中没有找到 'Time' 列。")

    except Exception as e:
        print(f"处理文件时发生错误: {e}")