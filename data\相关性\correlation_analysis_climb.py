import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from tqdm import tqdm
import os
import shutil

def run_analysis(method_name: str, df: pd.DataFrame, output_dir: Path):
    """
    通用分析函数，根据给定的方法名执行相关性分析并保存结果。
    
    :param method_name: 相关性方法名 ('kendall' or 'pearson')
    :param df: 用于分析的数据框 (只包含数值列)
    :param output_dir: 输出结果的目录
    """
    print(f"\n--- 开始执行 {method_name.capitalize()} 相关性分析 ---")
    
    # --- 1. 计算相关性矩阵 ---
    print(f"正在计算 {method_name.capitalize()} 相关性矩阵...")
    correlation_matrix = df.corr(method=method_name)

    # --- 2. 输出表格 ---
    table_output_path = output_dir / f'{method_name}_correlation_matrix.csv'
    correlation_matrix.to_csv(table_output_path, encoding='utf-8-sig')
    print(f"  {method_name.capitalize()} 相关性矩阵表格已保存到: {table_output_path.name}")

    # --- 3. 生成并保存热力图 ---
    print("  正在生成热力图...")
    num_params = len(df.columns)
    figsize = max(15, num_params * 0.7), max(12, num_params * 0.7)
    
    plt.figure(figsize=figsize)
    plt.imshow(correlation_matrix, cmap='coolwarm', interpolation='none')
    plt.colorbar()

    plt.xticks(range(num_params), correlation_matrix.columns, rotation=90, fontsize=10)
    plt.yticks(range(num_params), correlation_matrix.columns, fontsize=10)
    plt.title(f'所有参数的 {method_name.capitalize()} 相关性热力图', fontsize=20)

    if num_params <= 30:
        for i in range(num_params):
            for j in range(num_params):
                val = correlation_matrix.iloc[i, j]
                color = 'white' if abs(val) > 0.6 else 'black'
                plt.text(j, i, f'{val:.2f}', ha='center', va='center', color=color, fontsize=8)

    heatmap_output_path = output_dir / f'{method_name}_correlation_heatmap.png'
    plt.tight_layout()
    plt.savefig(heatmap_output_path, dpi=200)
    plt.close()
    
    print(f"  热力图已保存到: {heatmap_output_path.name}")

    # --- 4. 新增: 生成与IAS相关的排序图 ---
    target_feature = 'IAS'
    if target_feature in df.columns:
        print(f"  正在生成与 {target_feature} 相关的相关性排序图...")
        
        # 提取与IAS的相关性，并去掉IAS自身
        correlations_with_target = correlation_matrix[target_feature].drop(target_feature)
        
        # 按相关性降序排序
        sorted_correlations = correlations_with_target.sort_values(ascending=False)
        
        plt.figure(figsize=(16, 8))
        sorted_correlations.plot(kind='bar', color='sandybrown', zorder=2)
        
        # 添加阈值线
        plt.axhline(y=0.4, color='red', linestyle='--', linewidth=1.2, zorder=3)
        plt.axhline(y=-0.4, color='red', linestyle='--', linewidth=1.2, zorder=3)
        
        plt.title(f'各特征与 {target_feature} 的 {method_name.capitalize()} 相关性排序', fontsize=20)
        plt.ylabel('相关系数', fontsize=12)
        plt.xlabel('特征参数', fontsize=12)
        plt.xticks(rotation=45, ha='right', fontsize=10)
        plt.grid(axis='y', linestyle='--', alpha=0.7, zorder=1)
        plt.tight_layout()
        
        ranking_plot_path = output_dir / f'{method_name}_correlation_ranking_with_{target_feature}.png'
        plt.savefig(ranking_plot_path, dpi=200)
        plt.close()
        
        print(f"  排序图已保存到: {ranking_plot_path.name}")

        # --- 5. 新增: 根据排序选出Top N正/负相关特征 ---
        print(f"  正在选出与 {target_feature} 最相关的特征...")
        
        # 选出最强的10个正相关特征
        top_10_positive = sorted_correlations.head(10)
        
        # 选出最强的5个负相关特征 (tail会给出最小的值，即最负的值)
        top_5_negative = sorted_correlations.tail(5)
        
        # 合并特征名称为一个列表
        selected_features_list = top_10_positive.index.tolist() + top_5_negative.index.tolist()
        
        print(f"  选出的15个最相关特征列表: {selected_features_list}")
        
        # 将详细列表保存到文件
        list_output_path = output_dir / f'{method_name}_top_features_for_{target_feature}.txt'
        with open(list_output_path, 'w', encoding='utf-8') as f:
            f.write(f"与 {target_feature} 相关性最强的特征列表 ({method_name.capitalize()} 方法)\n")
            f.write("="*40 + "\n\n")
            
            f.write("Top 10 正相关特征:\n")
            for feature, corr_value in top_10_positive.items():
                f.write(f"- {feature:<15}: {corr_value:.4f}\n")
            
            f.write("\nTop 5 负相关特征:\n")
            for feature, corr_value in top_5_negative.items():
                 f.write(f"- {feature:<15}: {corr_value:.4f}\n")
            
            f.write("\n" + "="*40 + "\n")
            f.write("合并后的特征名称 (可直接用于代码):\n")
            # 为了方便复制粘贴，将列表格式化输出
            f.write("[\n")
            for i, feature in enumerate(selected_features_list):
                f.write(f"    '{feature}',\n")
            f.write("]\n")

        print(f"  详细特征列表已保存到: {list_output_path.name}")
        
    else:
        print(f"  警告: 目标特征 '{target_feature}' 不在数据中，跳过排序图和特征选择。")


def perform_all_analyses():
    """
    对 'data/爬升汇总.csv' 的数据，
    并同时使用肯德尔(Kendall)和皮尔逊(Pearson)方法进行相关性分析。
    """
    # --- 路径定义 ---
    project_root = Path(__file__).resolve().parent.parent
    input_file = project_root / 'data' / '爬升汇总_筛.csv'
    output_dir_name = "climb_correlation_analysis_results"
    output_dir = project_root / output_dir_name
    
    if output_dir.exists():
        shutil.rmtree(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    print(f"分析结果将保存在: {output_dir}")

    # --- 数据加载 ---
    if not input_file.exists():
        print(f"错误: 输入文件 '{input_file}' 不存在。")
        return

    print(f"正在从 '{input_file.name}' 加载爬升段数据...")
    df = pd.read_csv(input_file)

    # --- 数据筛选 ---
    numeric_df = df.select_dtypes(include=np.number)
    cols_to_exclude = ['段号', 'Longitude', 'Latitude']
    final_df = numeric_df[[col for col in numeric_df.columns if col not in cols_to_exclude]]
    
    if final_df.empty:
        print("错误：筛选后没有可用于分析的数值列。")
        return
        
    print(f"将对以下 {len(final_df.columns)} 个参数进行相关性分析...")
    
    # --- 依次执行两种分析 ---
    run_analysis('kendall', final_df, output_dir)
    run_analysis('pearson', final_df, output_dir)
    
    print("\n所有相关性分析已全部完成!")

if __name__ == '__main__':
    # 设置matplotlib支持中文显示
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
    perform_all_analyses() 