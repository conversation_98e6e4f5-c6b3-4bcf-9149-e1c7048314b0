import pandas as pd
import numpy as np

def add_transient_anomaly(df, anomaly_center_index, duration, anomaly_value):
    """
    在给定的中心点周围注入一个持续多点的瞬时V型异常。
    这种异常模拟了一个真实世界中的短暂、剧烈的传感器读数变化。

    Args:
        df (pd.DataFrame): 要修改的DataFrame。
        anomaly_center_index (int): 异常最低点的行索引。
        duration (int): 异常影响的总点数 (建议为奇数，如3, 5, 7)。
        anomaly_value (float): 异常最低点的值。
    
    Returns:
        pd.DataFrame: 修改后的DataFrame。
    """
    if duration % 2 == 0:
        print(f"警告: 持续时间 {duration} 是偶数，建议使用奇数以获得对称的V型异常。")
    
    half_duration = duration // 2
    start_index = anomaly_center_index - half_duration
    end_index = anomaly_center_index + half_duration

    if start_index < 0 or end_index >= len(df):
        print(f"警告: 在索引 {anomaly_center_index} 处的异常窗口超出了数据范围，跳过此事件。")
        return df

    # 获取异常发生前的正常值作为参考，我们取异常窗口开始前一个点的值
    # 这样做可以确保异常结束后能平滑地恢复到原始趋势
    pre_anomaly_value = df.loc[start_index - 1, 'IAS']
    post_anomaly_value = df.loc[end_index + 1, 'IAS']


    # 生成从正常值到异常值的线性下降序列
    drop_sequence = np.linspace(pre_anomaly_value, anomaly_value, num=half_duration + 1)
    
    # 生成从异常值到正常值的线性恢复序列
    recover_sequence = np.linspace(anomaly_value, post_anomaly_value, num=half_duration + 2) # +2 because we need to include the start and end point

    # 注入异常
    # 1. 下降部分 (包括最低点)
    for i in range(half_duration + 1):
        idx = start_index + i
        df.loc[idx, 'IAS'] = drop_sequence[i]
        df.loc[idx, 'is_anomaly'] = 1

    # 2. 恢复部分 (不包括最低点)
    for i in range(1, half_duration + 1):
        idx = anomaly_center_index + i
        df.loc[idx, 'IAS'] = recover_sequence[i]
        df.loc[idx, 'is_anomaly'] = 1
        
    print(f"在索引 {anomaly_center_index} 注入了持续 {duration} 个点的瞬时异常，最低值为 {anomaly_value:.2f}。")
    return df

# --- 配置 ---
# 定义要修改的文件路径。
# 注意：为了让测试脚本 test_all_xiangxian_IAS.py 能找到这个文件，
# 我们直接修改它当前引用的文件 'data/爬升/48_IAS.csv'。
file_path = 'data/爬升/48_IAS.csv' 

# 定义要注入的异常事件列表
# 每个事件: {'center_index': 最低点索引, 'duration': 持续点数, 'value': 最低点的值}
anomaly_events = [
    {'center_index': 86, 'duration': 5, 'value': 50},  # 在第87行达到最低点，异常持续5个点
    {'center_index': 172, 'duration': 5, 'value': 33} # 在第173行达到最低点，异常持续5个点
]

# --- 执行 ---
print(f"--- 开始向文件 '{file_path}' 注入科学的瞬时异常 ---")
try:
    # 在修改前，我们先备份原始文件
    original_df = pd.read_csv(file_path)
    backup_path = file_path.replace('.csv', '_backup.csv')
    original_df.to_csv(backup_path, index=False, encoding='utf-8-sig')
    print(f"原始文件已备份到: {backup_path}")
    df = original_df.copy()
except FileNotFoundError:
    print(f"错误: 文件未找到 {file_path}")
    exit()

# 添加 'is_anomaly' 列, 默认为0
if 'is_anomaly' not in df.columns:
    df['is_anomaly'] = 0
else:
    # 如果列已存在，先重置为0，以防重复运行脚本
    df['is_anomaly'] = 0

# 循环注入所有异常事件
for event in anomaly_events:
    df = add_transient_anomaly(
        df, 
        event['center_index'], 
        event['duration'], 
        event['value']
    )

# 将修改后的内容写回文件
df.to_csv(file_path, index=False, encoding='utf-8-sig')

print(f"\n文件 '{file_path}' 已成功修改。现在您可以重新运行测试脚本来评估新异常的检测效果。") 