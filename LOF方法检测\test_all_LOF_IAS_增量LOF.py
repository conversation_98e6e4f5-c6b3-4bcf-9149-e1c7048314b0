import pandas as pd
import torch
from sklearn.preprocessing import MinMaxScaler, StandardScaler
import os
import sys
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import json
from sklearn.neighbors import LocalOutlierFactor
from sklearn.metrics import pairwise_distances

# --- 新增：动态将项目根目录添加到Python路径 ---
# 获取当前脚本的绝对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录 (假设此脚本位于根目录下的 'LOF方法检测' 文件夹中)
project_root = os.path.dirname(current_dir)
# 将项目根目录添加到sys.path
if project_root not in sys.path:
    sys.path.insert(0, project_root)


# --- 1. 修改: 从我们现有的 "多对一" 脚本中导入配置 ---
from myinformer_IAS import TrainingConfig, Namespace
from models import Informer
from utils.timefeatures import time_features


# 解决Matplotlib画图中文显示问题
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use("ggplot")


# --- 优化: 高效的增量LOF实现 ---
class IncrementalLOF:
    def __init__(self, n_neighbors=20, initial_data=None):
        """
        一个更高效的增量LOF实现。
        - 缓存k-距离、邻居和LRD分数。
        - 当新点加入时，只更新受影响的邻居。
        """
        self.n_neighbors = n_neighbors
        self.data = []
        # 缓存
        self.k_distances = {} # 缓存每个点的k-距离
        self.neighbors = {}   # 缓存每个点的邻居列表
        self.lrd_scores = {}  # 缓存每个点的局部可达密度
        self.lof_scores = {}  # 缓存每个点的LOF分数

        if initial_data is not None and len(initial_data) > 0:
            print(f"Initializing IncrementalLOF with {len(initial_data)} data points...")
            self.data = list(initial_data)
            self._batch_update() # 对初始数据进行一次完整的计算

    def _find_k_nearest_neighbors(self, point_index):
        """为单个点计算k-近邻"""
        distances = pairwise_distances([self.data[point_index]], self.data)[0]
        # 排除自身，对距离进行排序
        neighbor_indices = np.argsort(distances)
        # 返回k个最近的邻居（索引和距离）
        return neighbor_indices[1:self.n_neighbors + 1]

    def _update_k_distance_and_neighbors(self, point_index):
        """计算并缓存一个点的k-距离和邻居列表"""
        neighbor_indices = self._find_k_nearest_neighbors(point_index)
        self.neighbors[point_index] = neighbor_indices
        
        if len(neighbor_indices) >= self.n_neighbors:
            # k-距离是到第k个邻居的距离
            k_dist_point = self.data[neighbor_indices[-1]]
            self.k_distances[point_index] = pairwise_distances([self.data[point_index]], [k_dist_point])[0][0]
        else:
            self.k_distances[point_index] = np.inf


    def _update_lrd(self, point_index):
        """计算并缓存一个点的局部可达密度(LRD)"""
        neighbor_indices = self.neighbors.get(point_index, [])
        if len(neighbor_indices) == 0:
            self.lrd_scores[point_index] = 0
            return

        total_reach_dist = 0
        for neighbor_idx in neighbor_indices:
            # 可达距离 = max(k-distance(neighbor), dist(point, neighbor))
            dist_p_n = pairwise_distances([self.data[point_index]], [self.data[neighbor_idx]])[0][0]
            # 从缓存中获取邻居的k-距离
            neighbor_k_dist = self.k_distances.get(neighbor_idx, np.inf)
            reach_dist = max(neighbor_k_dist, dist_p_n)
            total_reach_dist += reach_dist
        
        if total_reach_dist == 0:
            self.lrd_scores[point_index] = np.inf
        else:
            self.lrd_scores[point_index] = len(neighbor_indices) / total_reach_dist

    def _update_lof(self, point_index):
        """计算并缓存一个点的LOF分数"""
        neighbor_indices = self.neighbors.get(point_index, [])
        if len(neighbor_indices) == 0:
            self.lof_scores[point_index] = 1 # 孤立点
            return

        # 计算邻居的LRD均值
        neighbor_lrd_sum = sum(self.lrd_scores.get(n_idx, 0) for n_idx in neighbor_indices)
        avg_neighbor_lrd = neighbor_lrd_sum / len(neighbor_indices)
        
        # LOF = (邻居LRD均值) / (自身LRD)
        point_lrd = self.lrd_scores.get(point_index, 1e-9) # 避免除以零
        if point_lrd == 0:
             self.lof_scores[point_index] = 1
        else:
            self.lof_scores[point_index] = avg_neighbor_lrd / point_lrd

    def _batch_update(self):
        """对当前所有数据点进行批量计算，并填充缓存"""
        print("Performing batch update for k-distances and neighbors...")
        for i in range(len(self.data)):
            self._update_k_distance_and_neighbors(i)
        
        print("Performing batch update for LRD scores...")
        for i in range(len(self.data)):
            self._update_lrd(i)
        
        print("Performing batch update for LOF scores...")
        for i in range(len(self.data)):
            self._update_lof(i)
        print("Batch update complete.")

    def add_point(self, new_point):
        """
        增量添加新数据点并高效地更新相关分数。
        """
        new_idx = len(self.data)
        self.data.append(new_point)
        
        # 1. 为新点计算k-距离和邻居
        self._update_k_distance_and_neighbors(new_idx)

        # 2. 识别受影响的点的集合
        affected_indices = set()
        # a) 新点的邻居是受影响的
        affected_indices.update(self.neighbors.get(new_idx, []))
        # b) 检查哪些旧点可能将新点作为其新邻居
        new_point_vec = np.array(self.data[new_idx]).reshape(1, -1)
        for i in range(new_idx):
            # 如果一个旧点到新点的距离小于其当前的k-距离，那么它很可能受到影响
            dist_to_new = pairwise_distances(np.array(self.data[i]).reshape(1,-1), new_point_vec)[0][0]
            if dist_to_new < self.k_distances.get(i, np.inf):
                affected_indices.add(i)
        
        # 3. 为主要受影响的点更新k-距离和邻居
        for i in affected_indices:
            self._update_k_distance_and_neighbors(i)
            
        # 4. 准备一个更大的集合来更新LRD和LOF
        # 这个集合包括主要受影响的点，以及它们的邻居（因为邻居的LOF计算依赖于它们）
        update_set_for_lrd_lof = set(affected_indices)
        for i in affected_indices:
            update_set_for_lrd_lof.update(self.neighbors.get(i, []))
        # 新点本身也需要更新
        update_set_for_lrd_lof.add(new_idx)

        # 5. 为这个集合中的所有点更新LRD和LOF
        for i in update_set_for_lrd_lof:
            self._update_lrd(i)
        for i in update_set_for_lrd_lof:
            self._update_lof(i)

        return self.lof_scores.get(new_idx, 1.0)


    def get_scores(self):
        """返回所有当前点的LOF分数列表"""
        return [self.lof_scores.get(i, 1) for i in range(len(self.data))]


def load_and_process_segmented_data_for_test(file_path, config, scaler, feature_cols):
    """
    一个专门为 "多对一" 测试脚本修改的数据处理函数。
    """
    print(f"--- 开始处理测试文件: {file_path} ---")
    df = pd.read_csv(file_path)

    # --- 新增: 检查并加载真实异常标签 ---
    if 'is_anomaly' in df.columns:
        print("发现 'is_anomaly' 列，将用作异常检测评估的真实标签。")
        anomaly_labels = df['is_anomaly'].values
    else:
        print("未发现 'is_anomaly' 列，将假设所有点均为正常点。")
        anomaly_labels = np.zeros(len(df))

    df['datetime'] = pd.to_datetime(df[config.time_column], format='%H:%M:%S')

    df_stamp = df[['datetime']].copy()
    df_stamp.columns = ['date']
    time_marks = time_features(pd.DataFrame(df_stamp), timeenc=1, freq=config.freq)

    df_features = df[feature_cols]
    scaled_features = scaler.transform(df_features)
    
    # --- 2. 修改: 增加获取目标列索引的逻辑 ---
    try:
        target_col_index = feature_cols.index(config.target_feature)
    except ValueError:
        raise ValueError(f"错误: 目标特征 '{config.target_feature}' 不在特征列表 feature_cols 中。")

    print("正在根据 '起点' 和 '终点' 标记识别飞行段...")
    segments = []
    segment_start_index = -1
    for i, row in df.iterrows():
        marker = str(row[config.segment_marker_column])
        if '起点' in marker:
            segment_start_index = i
        elif '终点' in marker and segment_start_index != -1:
            segment_df = df.loc[segment_start_index:i].copy()
            segments.append(segment_df)
            segment_start_index = -1
            
    print(f"在文件 {file_path} 中识别出 {len(segments)} 个有效飞行段。")
    
    all_x, all_y, all_x_mark, all_y_mark = [], [], [], []
    segment_window_counts = [] 
    all_target_times = []
    initial_segments_for_plot = [] 
    all_ground_truth = [] # <--- 新增

    for segment_df in segments:
        if len(segment_df) >= config.seq_len:
            initial_data_dict = {
                'values': segment_df.iloc[:config.seq_len][config.target_feature].tolist(),
                'times': segment_df.iloc[:config.seq_len]['datetime'].tolist()
            }
            initial_segments_for_plot.append(initial_data_dict)
        else:
            initial_segments_for_plot.append(None)

        segment_indices = segment_df.index
        current_segment_features = scaled_features[segment_indices]
        current_segment_marks = time_marks[segment_indices] if time_marks is not None else np.empty_like(scaled_features[segment_indices]) # Ensure time_marks is not None
        
        total_sequence_length = config.seq_len + config.pred_len
        
        if len(current_segment_features) < total_sequence_length:
            segment_window_counts.append(0)
            continue

        num_windows_in_segment = 0
        for i in range(len(current_segment_features) - total_sequence_length + 1):
            x_seq = current_segment_features[i : i + config.seq_len]
            x_mark_seq = current_segment_marks[i : i + config.seq_len]
            y_start_index = i + config.seq_len - config.label_len
            y_end_index = i + total_sequence_length
            
            # --- 3. 修改: y_seq只提取目标特征那一列 ---
            y_seq_full = current_segment_features[y_start_index : y_end_index]
            y_seq_target = y_seq_full[:, target_col_index:target_col_index+1]
            
            y_mark_seq = current_segment_marks[y_start_index : y_end_index]

            all_x.append(x_seq)
            all_x_mark.append(x_mark_seq)
            all_y.append(y_seq_target) # <--- 使用只包含目标特征的y
            all_y_mark.append(y_mark_seq)
            
            target_original_index = segment_df.index[i + total_sequence_length - 1]
            all_target_times.append(df.loc[target_original_index, 'datetime'])
            
            # --- 新增: 收集真实标签 ---
            all_ground_truth.append(anomaly_labels[target_original_index])
            
            num_windows_in_segment += 1
        
        segment_window_counts.append(num_windows_in_segment)
            
    total_windows = len(all_x)
    print(f"文件 {file_path} 处理完毕，共生成 {total_windows} 个有效窗口。")

    final_x = torch.tensor(np.array(all_x), dtype=torch.float32)
    final_y = torch.tensor(np.array(all_y), dtype=torch.float32)
    final_x_mark = torch.tensor(np.array(all_x_mark), dtype=torch.float32)
    final_y_mark = torch.tensor(np.array(all_y_mark), dtype=torch.float32)
    final_ground_truth = torch.tensor(np.array(all_ground_truth), dtype=torch.long)
    
    return final_x, final_y, final_x_mark, final_y_mark, segment_window_counts, all_target_times, initial_segments_for_plot, final_ground_truth


def test_and_plot_predictions(model, loader, scaler, config, device, output_dir, feature_cols, segment_lengths=None, target_times=None, initial_segments_for_plot=None):
    """
    --- 4. 重构: 整个函数被重构以适应单变量预测和评估 ---
    """
    print(f"\n--- 开始在测试集上评估和预测唯一目标: {config.target_feature} ---")
    model.eval()
    all_preds, all_trues, all_ground_truth = [], [], []
    with torch.no_grad():
        for i, (batch_x, batch_y, batch_x_mark, batch_y_mark, batch_ground_truth) in enumerate(loader):
            batch_x, batch_y, batch_x_mark, batch_y_mark = \
                batch_x.to(device), batch_y.to(device), batch_x_mark.to(device), batch_y_mark.to(device)
            
            dec_inp = torch.zeros_like(batch_y[:, -config.pred_len:, :]).float()
            dec_inp = torch.cat([batch_y[:, :config.label_len, :], dec_inp], dim=1).float().to(device)
            
            outputs = model(batch_x, batch_x_mark, dec_inp, batch_y_mark)
            
            true_targets = batch_y[:, -config.pred_len:, :]
            pred_targets = outputs
            
            all_preds.append(pred_targets.detach().cpu().numpy())
            all_trues.append(true_targets.detach().cpu().numpy())
            all_ground_truth.append(batch_ground_truth.cpu().numpy())

    preds = np.concatenate(all_preds, axis=0)
    trues = np.concatenate(all_trues, axis=0)
    ground_truth_labels = np.concatenate(all_ground_truth, axis=0).flatten()

    # --- 核心修改: 将异常判据从 "瞬时误差" 改为 "轨迹MSE" ---
    print("\n--- 策略: 基于预测轨迹与真实轨迹的MSE进行异常检测 ---")

    # 1. 反归一化整个预测轨迹
    num_windows, pred_len, _ = preds.shape
    num_features = len(feature_cols)
    target_col_index = feature_cols.index(config.target_feature)

    def unscale_trajectories(scaled_traj, scaler, target_idx, num_feat):
        """一个帮助函数，用于反归一化整个轨迹。"""
        # Reshape for scaler: from (N, L, 1) to (N*L, 1)
        scaled_flat = scaled_traj.reshape(-1, 1)
        # Create dummy array for inverse transform
        dummy_array = np.zeros((scaled_flat.shape[0], num_feat))
        dummy_array[:, target_idx] = scaled_flat.squeeze()
        # Inverse transform
        unscaled_full = scaler.inverse_transform(dummy_array)
        # Extract the unscaled target feature and reshape back
        unscaled_flat = unscaled_full[:, target_idx]
        return unscaled_flat.reshape(scaled_traj.shape[0], scaled_traj.shape[1])

    trues_unscaled_traj = unscale_trajectories(trues, scaler, target_col_index, num_features)
    preds_unscaled_traj = unscale_trajectories(preds, scaler, target_col_index, num_features)

    # 2. 计算每个窗口的MSE作为异常分数
    errors = np.mean((trues_unscaled_traj - preds_unscaled_traj) ** 2, axis=1)
    print(f"已计算所有窗口的轨迹MSE，将作为LOF算法的输入。MSE范围: [{errors.min():.4f}, {errors.max():.4f}]")

    # 3. 为绘图准备单点真值和预测值 (取轨迹的最后一个点)
    feature_trues = trues_unscaled_traj[:, -1]
    feature_preds = preds_unscaled_traj[:, -1]

    # --- 增量LOF 异常检测模块 ---
    print("\n--- 正在使用增量 LOF 算法进行异常检测 ---")

    # --- 特征工程更新: 基于轨迹MSE构建特征 ---
    print("创建二维特征: [轨迹MSE, 轨迹MSE变化率]")
    error_gradients = np.gradient(errors)
    features_for_lof = np.stack([errors, error_gradients], axis=1)

    # --- 对(二维)误差特征进行标准化 ---
    print("正在对二维误差特征进行Z-score标准化...")
    scaler_lof = StandardScaler()
    errors_scaled = scaler_lof.fit_transform(features_for_lof)
    
    # --- 新增: 从NPSR脚本移植过来的高效F1阈值搜索函数 (带Precision约束) ---
    def get_best_f1_threshold(scores, labels, min_precision=0.8):
        """
        高效F1阈值搜索算法。
        在满足最小精确度要求的前提下，寻找最优F1分数对应的阈值。
        假设分数越高越异常。
        """
        scores = np.asarray(scores).flatten()
        labels = labels.flatten()
        
        # LOF分数越高越异常，所以按分数升序排序
        combined = sorted(zip(scores, labels), key=lambda x: x[0])
        sorted_scores, sorted_labels = zip(*combined)
        sorted_labels = np.array(sorted_labels)
        
        total_positives = np.sum(sorted_labels)
        if total_positives == 0:
            print("警告: 真实标签中没有异常点，无法计算F1分数。")
            return -1, -1

        tp = total_positives
        fp = len(labels) - total_positives
        fn = 0
        
        best_f1 = -1
        best_threshold = -1

        # 从将所有点都判为异常开始，逐渐提高阈值
        for i in range(len(sorted_scores) - 1):
            # 阈值移动，一个点从异常堆移动到正常堆
            if sorted_labels[i] == 1:  # 如果这个点是真异常
                tp -= 1
                fn += 1
            else:  # 如果这个点是真正常
                fp -= 1

            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            
            # --- 新增约束: 只有当精确度满足要求时才考虑 ---
            if precision > min_precision:
                recall = tp / (tp + fn) if (tp + fn) > 0 else 0
                f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
                
                # 更新最优F1和阈值
                if f1 >= best_f1:
                    # 只有当分数变化时才更新阈值
                    if sorted_scores[i] < sorted_scores[i+1]:
                        best_f1 = f1
                        best_threshold = (sorted_scores[i] + sorted_scores[i+1]) / 2
        
        if best_threshold == -1:
            print(f"警告: 在 Precision > {min_precision} 的约束下，未找到任何有效的阈值。")

        return best_f1, best_threshold

    # --- 优化策略更新: 动态搜索最优 n_neighbors 和 阈值 以最大化F1分数 ---
    print("\n--- 开始动态搜索最优 n_neighbors 和 阈值以最大化F1分数 (要求 Precision > 0.8) ---")
    
    global_best_f1 = -1
    best_n_neighbors = -1
    best_threshold = -1
    final_anomaly_scores = None

    # 1. 遍历 n_neighbors 的候选值 - 扩大搜索范围和减小步长以进行更精细的优化
    for n_neighbors_candidate in range(10, 81, 2):
        print(f"\n--- 正在测试 n_neighbors = {n_neighbors_candidate} ---")
        
        # 2. 使用增量LOF计算分数
        # 为了模拟真实场景，我们假设数据是流式到达的
        # 但为了找到最优参数，我们这里先用全量数据进行一次模拟
        initial_data_size = max(n_neighbors_candidate + 1, int(0.1 * len(errors_scaled))) # 初始化的数据点数
        initial_data = errors_scaled[:initial_data_size]
        
        inc_lof = IncrementalLOF(n_neighbors=n_neighbors_candidate, initial_data=initial_data)

        # 增量地添加剩余的点
        for i in range(initial_data_size, len(errors_scaled)):
            inc_lof.add_point(errors_scaled[i])
        
        current_anomaly_scores = np.array(inc_lof.get_scores())
        
        # 3. 使用高效函数找到当前 n_neighbors 下的最优F1和对应阈值
        # 注意: 增量LOF分数越高越异常
        current_f1, current_threshold = get_best_f1_threshold(current_anomaly_scores, ground_truth_labels, min_precision=0.8)

        if current_threshold != -1 and current_f1 != -1:
            print(f"结果: n_neighbors={n_neighbors_candidate} -> (P>0.8) 最优F1={current_f1:.4f}")

            # 4. 如果当前F1分数是全局最优的，则更新全局参数
            if current_f1 > global_best_f1:
                global_best_f1 = current_f1
                best_n_neighbors = n_neighbors_candidate
                best_threshold = current_threshold
                final_anomaly_scores = current_anomaly_scores
        else:
            print(f"结果: n_neighbors={n_neighbors_candidate} -> 未找到满足P>0.8的有效阈值。")

    print("\n--- 参数搜索完成 ---")
    if best_n_neighbors != -1:
        feature_threshold = best_threshold
        anomaly_scores = final_anomaly_scores
        print(f"最终采用的最优参数: n_neighbors={best_n_neighbors}, 对应的LOF分数阈值为 > {feature_threshold:.4f}")
        print(f"在此参数下达到的全局最优F1分数为: {global_best_f1:.4f}")
    else:
        print("警告: 未能找到任何满足P>0.8的有效参数组合，将使用默认值进行预测。")
        # 回退逻辑
        inc_lof = IncrementalLOF(n_neighbors=35, initial_data=errors_scaled)
        anomaly_scores = np.array(inc_lof.get_scores())
        feature_threshold = np.median(anomaly_scores) if anomaly_scores.size > 0 else 0.5
        
    # --- 使用最优阈值计算最终的异常点 ---
    # LOF分数越高越异常
    predicted_anomalies = (anomaly_scores > feature_threshold).astype(int) if anomaly_scores is not None else np.zeros_like(ground_truth_labels)
    
    TP = np.sum((predicted_anomalies == 1) & (ground_truth_labels == 1))
    FP = np.sum((predicted_anomalies == 1) & (ground_truth_labels == 0))
    TN = np.sum((predicted_anomalies == 0) & (ground_truth_labels == 0))
    FN = np.sum((predicted_anomalies == 0) & (ground_truth_labels == 1))
    
    # --- 新增: 计算评估指标 ---
    TPR = TP / (TP + FN) if (TP + FN) > 0 else 0  # 检测率 (Recall)
    FPR = FP / (FP + TN) if (FP + TN) > 0 else 0  # 误检率
    Pre = TP / (TP + FP) if (TP + FP) > 0 else 0  # 精确度
    Acc = (TP + TN) / (TP + TN + FP + FN) if (TP + TN + FP + FN) > 0 else 0 # 准确率
    F1 = 2 * Pre * TPR / (Pre + TPR) if (Pre + TPR) > 0 else 0 # F1分数
    
    print("\n--- 异常检测评估指标 ---")
    print(f"TP: {TP}, FP: {FP}, TN: {TN}, FN: {FN}")
    print(f"检测率 (TPR/Recall): {TPR:.4f}")
    print(f"误检率 (FPR): {FPR:.4f}")
    print(f"精确度 (Precision): {Pre:.4f}")
    print(f"准确率 (Accuracy): {Acc:.4f}")
    print(f"F1 分数: {F1:.4f}")
    
    anomaly_metrics = {
        'TP': TP, 'FP': FP, 'TN': TN, 'FN': FN,
        'TPR': TPR, 'FPR': FPR, 'Precision': Pre,
        'Accuracy': Acc, 'F1_Score': F1,
        'Threshold': feature_threshold
    }
    
    # --- 保存异常检测指标到单独的CSV
    anomaly_metrics_df = pd.DataFrame([anomaly_metrics])
    anomaly_metrics_save_path = os.path.join(output_dir, 'test_anomaly_evaluation_metrics.csv')
    anomaly_metrics_df.to_csv(anomaly_metrics_save_path, index=False, encoding='utf-8-sig')
    print(f"异常检测评估指标已保存到: {anomaly_metrics_save_path}")

    all_anomalies_report = []
    
    # --- 6. 简化: 计算唯一的评估指标 ---
    r2 = r2_score(feature_trues, feature_preds)
    mse = mean_squared_error(feature_trues, feature_preds)
    mae = mean_absolute_error(feature_trues, feature_preds)
    print(f"测试集评估结果 ({config.target_feature}) - R2: {r2:.4f}, MSE: {mse:.4f}, MAE: {mae:.4f}")
    
    metrics_data = [{'Feature': config.target_feature, 'R2': r2, 'MSE': mse, 'MAE': mae}]
    
    # --- 7. 简化: 绘制唯一的预测结果图 ---
    # Ensure segment_lengths, target_times, initial_segments_for_plot are not None
    valid_segments_to_plot = [l for l in segment_lengths if l > 0] if segment_lengths is not None else []
    n_segments = len(valid_segments_to_plot)

    if n_segments > 0:
        if n_segments == 1: n_cols = 1; figsize = (12, 5)
        else: n_cols = 2; figsize = (15, ((n_segments + 1) // 2) * 4)
        n_rows = (n_segments + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize, squeeze=False)
        fig.suptitle(f'TEST SET: Segment-wise Prediction vs Real ({config.target_feature})', fontsize=16)
        
        current_idx = 0
        plot_idx = 0
        for seg_idx, length in enumerate(segment_lengths if segment_lengths is not None else []):
            if length == 0: continue
            
            initial_plot_data = initial_segments_for_plot[seg_idx] if initial_segments_for_plot is not None else None
            
            segment_trues = feature_trues[current_idx : current_idx + length]
            segment_preds = feature_preds[current_idx : current_idx + length]
            segment_times = target_times[current_idx : current_idx + length] if target_times is not None else []
            
            row, col = plot_idx // n_cols, plot_idx % n_cols
            ax = axes[row, col]

            # 绘制历史和真实值
            if initial_plot_data and initial_plot_data.get('times') and initial_plot_data.get('values'):
                full_real_times = initial_plot_data['times'] + segment_times
                full_real_values = initial_plot_data['values'] + list(segment_trues)
                ax.plot(full_real_times, full_real_values, label='Real Value (含历史)', color='#ff7f0e')
                ax.axvline(x=initial_plot_data['times'][-1], color='red', linestyle='--', linewidth=1.5, label='预测起点')
            else:
                 ax.plot(segment_times, segment_trues, label='Real Value', color='#ff7f0e')

            # 绘制预测值
            ax.plot(segment_times, segment_preds, label='Predicted Value', color='#1f77b4', linewidth=2)
            
            # --- 新增: 在图上标记异常点 (基于LOF结果) ---
            segment_predicted_anomalies = predicted_anomalies[current_idx : current_idx + length]
            anomaly_indices_in_segment = np.where(segment_predicted_anomalies == 1)[0]
            
            if len(anomaly_indices_in_segment) > 0:
                anomaly_times = [segment_times[j] for j in anomaly_indices_in_segment]
                anomaly_values = segment_trues[anomaly_indices_in_segment]
                ax.scatter(anomaly_times, anomaly_values, color='red', s=50, zorder=5, label='异常点')
                
                # 记录异常信息到报告
                segment_lof_scores = anomaly_scores[current_idx : current_idx + length] if anomaly_scores is not None else np.array([])
                for anom_idx in anomaly_indices_in_segment:
                    all_anomalies_report.append({
                        'Time': segment_times[anom_idx].strftime('%H:%M:%S'),
                        'Feature': config.target_feature,
                        'RealValue': segment_trues[anom_idx],
                        'PredictedValue': segment_preds[anom_idx],
                        'AnomalyScore': segment_lof_scores[anom_idx],
                        'Threshold': feature_threshold
                    })

            ax.set_title(f"Test Segment {seg_idx+1}")
            ax.set_ylabel(config.target_feature)
            # 确保图例只显示一次，避免重复
            handles, labels = ax.get_legend_handles_labels()
            by_label = dict(zip(labels, handles))
            ax.legend(by_label.values(), by_label.keys())
            ax.grid(True)
            
            import matplotlib.dates as mdates
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
            plt.setp(ax.get_xticklabels(), rotation=30, ha="right")

            current_idx += length
            plot_idx += 1

        for j in range(plot_idx, n_rows * n_cols): fig.delaxes(axes.flatten()[j])
        plt.tight_layout(rect=(0.0, 0.03, 1.0, 0.95))
        save_path_img = os.path.join(output_dir, f'test_prediction_plot_{config.target_feature}.png')
        plt.savefig(save_path_img)
        print(f"预测结果图已保存到: {save_path_img}")
        plt.close(fig)

    # --- 新增：为LOF分类结果生成散点图 (修改为用户指定样式) ---
    fig_lof, ax_lof = plt.subplots(figsize=(8, 6))
    indices = np.arange(len(errors))

    # 为了绘图，将最终的0/1结果(predicted_anomalies)转换回-1/1格式
    # 异常(1) -> -1 (红色), 正常(0) -> 1 (蓝色) - 调整以匹配coolwarm
    plot_predictions = np.where(predicted_anomalies == 1, 1, -1)

    # 使用单一的scatter调用和cmap来匹配示例图的样式
    # 在plot_predictions中, 1是异常点(coolwarm中接近红色), -1是正常点(coolwarm中接近蓝色)
    scatter = ax_lof.scatter(indices, errors, c=plot_predictions, cmap='coolwarm', vmin=-1, vmax=1)

    ax_lof.set_title('Incremental LOF Anomaly Detection')
    ax_lof.set_xlabel('Data Point Index')
    ax_lof.set_ylabel('Trajectory MSE') # <-- 修改Y轴标签
    ax_lof.grid(True)
    
    # 创建图例
    legend_elements = [Line2D([0], [0], marker='o', color='w', label='Anomaly', markerfacecolor='red', markersize=8),
                       Line2D([0], [0], marker='o', color='w', label='Normal', markerfacecolor='blue', markersize=8)]
    ax_lof.legend(handles=legend_elements)


    save_path_lof_scatter = os.path.join(output_dir, f'test_lof_classification_plot_{config.target_feature}.png')
    plt.savefig(save_path_lof_scatter)
    print(f"LOF 分类检测图已保存到: {save_path_lof_scatter}")
    plt.close(fig_lof)


    # --- 8. 简化: 保存唯一的评估和预测结果 ---
    metrics_df = pd.DataFrame(metrics_data)
    metrics_save_path = os.path.join(output_dir, 'test_evaluation_metrics.csv')
    metrics_df.to_csv(metrics_save_path, index=False, encoding='utf-8-sig')
    print(f"\n评估指标已保存到: {metrics_save_path}")

    prediction_dfs = {'Time': [t.strftime('%H:%M:%S') for t in target_times] if target_times is not None else [],
                      f'Real_{config.target_feature}': feature_trues,
                      f'Predicted_{config.target_feature}': feature_preds}
    results_df = pd.DataFrame(prediction_dfs)
    results_save_path = os.path.join(output_dir, 'test_predictions.csv')
    results_df.to_csv(results_save_path, index=False, encoding='utf-8-sig')
    print(f"预测结果CSV已保存到: {results_save_path}")

    # --- 新增: 保存异常检测报告CSV ---
    if all_anomalies_report:
        anomalies_df = pd.DataFrame(all_anomalies_report)
        anomalies_df.sort_values(by=['Time', 'Feature'], inplace=True)
        anomalies_save_path = os.path.join(output_dir, 'test_anomalies_report.csv')
        anomalies_df.to_csv(anomalies_save_path, index=False, encoding='utf-8-sig')
        print(f"异常检测报告已保存到: {anomalies_save_path}")
    else:
        print("在本次测试中未检测到异常点。")

if __name__ == '__main__':
    # --- 9. 修改: 主函数逻辑全面适配 "多对一" 测试 ---
    config = TrainingConfig() # 使用 myinformer_.py 中的配置
    
    test_file_path = 'data/爬升/48_IAS.csv'
    
    # --- 核心修改: 将结果直接保存在 'LOF方法检测' 文件夹下 ---
    script_dir = os.path.dirname(os.path.abspath(__file__))
    test_results_dir = os.path.join(script_dir, 'test_results_single_target_增量LOF')
    os.makedirs(test_results_dir, exist_ok=True)
    print(f"测试结果将保存到: '{test_results_dir}'")
    
    # 结果将保存在 "多对一" 模型训练结果目录下的一个 'test_results' 子文件夹中
    model_dir = config.results_dir
    # 请确保这个路径指向你训练好的 "多对一" 模型
    model_path = os.path.join(model_dir, f'best_model_{config.target_feature}.pth')
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"未找到单变量模型, 请确认路径正确: {model_path}")
    if not os.path.exists(test_file_path):
         raise FileNotFoundError(f"未找到测试集文件: {test_file_path}")

    # --- 加载训练集以拟合缩放器并获取特征列表 ---
    print("\n--- 在原始训练集上重新拟合缩放器和确定特征 ---")
    df_train_raw = pd.read_csv(config.train_file_path)
    
    # --- <修改> 直接使用训练配置中定义的特征列表 ---
    feature_cols = config.use_features
    
    # 安全检查：确保所有指定的特征都存在于DataFrame中
    missing_in_df = [col for col in feature_cols if col not in df_train_raw.columns]
    if missing_in_df:
        raise ValueError(f"致命错误: 在训练数据中找不到以下指定的特征: {missing_in_df}")

    # 安全检查：确保目标特征在所选特征列表中
    if config.target_feature not in feature_cols:
        raise ValueError(f"致命错误: 目标特征 '{config.target_feature}' 不在指定的 use_features 列表中。")

    scaler = MinMaxScaler()
    scaler.fit(df_train_raw[feature_cols])
    print(f"缩放器拟合成功，将使用以下 {len(feature_cols)} 个特征作为输入: {feature_cols}")
    print(f"测试目标为: ['{config.target_feature}']")

    # --- 10. 修改: 确保在创建模型配置前更新动态参数 ---
    print("\n--- 准备模型配置 ---")
    # 在调用 to_informer_config() 之前，必须先设置好动态计算的参数
    config.data_dim = len(feature_cols)
    config.target_dim_index = feature_cols.index(config.target_feature)

    # 创建 Informer 模型的配置对象
    informer_config_obj = config.to_informer_config()

    # 确认参数已正确传递
    if not hasattr(informer_config_obj, 'enc_in') or informer_config_obj.enc_in is None:
        raise ValueError("模型配置错误: 'enc_in' 未被设置或为 None。")
    if not hasattr(informer_config_obj, 'target_dim_index'):
        raise ValueError("模型配置错误: 'target_dim_index' 未被设置。")

    print(f"模型配置创建成功。输入维度(enc_in): {informer_config_obj.enc_in}, 目标索引(target_dim_index): {getattr(informer_config_obj, 'target_dim_index', 'Not Found')}")

    # --- 加载已训练的模型 ---
    print("\n--- 加载已训练的 '多对一' 模型 ---")
    model = Informer.Model(informer_config_obj).to(device)
    
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.eval() 
    print(f"模型已从 {model_path} 加载")

    # --- 加载并处理测试数据 ---
    print("\n--- 处理测试数据 ---")
    test_x, test_y, test_x_mark, test_y_mark, test_segment_lengths, test_target_times, initial_segments_for_plot, test_ground_truth = \
        load_and_process_segmented_data_for_test(test_file_path, config, scaler, feature_cols)

    test_dataset = TensorDataset(test_x, test_y, test_x_mark, test_y_mark, test_ground_truth)
    test_loader = DataLoader(test_dataset, batch_size=config.batch_size, shuffle=False)
    print("测试数据加载完毕。")

    # --- 在测试集上评估并可视化结果 ---
    test_and_plot_predictions(
        model=model,
        loader=test_loader,
        scaler=scaler,
        config=config,
        device=device,
        output_dir=test_results_dir,
        feature_cols=feature_cols,
        segment_lengths=test_segment_lengths,
        target_times=test_target_times,
        initial_segments_for_plot=initial_segments_for_plot
    ) 