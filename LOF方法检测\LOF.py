import numpy as np
import matplotlib.pyplot as plt
from sklearn.datasets import make_blobs
from sklearn.neighbors import LocalOutlierFactor

# 生成模拟数据
X, _ = make_blobs(n_samples=300, centers=3, n_features=2, random_state=42)
print(X)
# 初始化LOF模型
lof = LocalOutlierFactor(n_neighbors=20, contamination=0.1)

# 训练模型并预测异常点
y_pred = lof.fit_predict(X)

# 可视化结果
plt.scatter(X[:, 0], X[:, 1], c=y_pred, cmap='coolwarm', marker='o')
plt.title('LOF Anomaly Detection')
plt.xlabel('Feature 1')
plt.ylabel('Feature 2')
plt.show()

# 打印异常点
outliers = X[y_pred == -1]
print("Detected outliers:")
print(outliers)