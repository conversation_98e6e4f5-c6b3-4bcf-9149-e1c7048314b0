import pandas as pd
import torch
from sklearn.preprocessing import MinMaxScaler
import os
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import json

# --- 1. 修改: 从我们现有的 "多对多" 脚本中导入配置 ---
from myinformer_轨迹 import TrainingConfig, Namespace
from models import Informer_convgru_优化轨迹 as Informer # <--- 修改: 使用轨迹优化后的模型
from utils.timefeatures import time_features


# 解决Matplotlib画图中文显示问题
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use("ggplot")


def load_and_process_segmented_data_for_test(file_path, config, scaler, feature_cols):
    """
    一个专门为 "多对多" 轨迹测试脚本修改的数据处理函数。
    """
    print(f"--- 开始处理测试文件: {file_path} ---")
    df = pd.read_csv(file_path)

    df['datetime'] = pd.to_datetime(df[config.time_column], format='%H:%M:%S')

    df_stamp = df[['datetime']].copy()
    df_stamp.rename(columns={'datetime': 'date'}, inplace=True)
    time_marks = time_features(df_stamp, timeenc=1, freq=config.freq)

    df_features = df[feature_cols]
    scaled_features = scaler.transform(df_features)
    
    try:
        if isinstance(config.target_feature, str):
            target_cols_indices = [feature_cols.index(config.target_feature)]
        else:
            target_cols_indices = [feature_cols.index(t) for t in config.target_feature]
    except ValueError as e:
        raise ValueError(f"错误: 目标特征 '{e.args[0]}' 不在特征列表 feature_cols 中。")

    print("正在根据 '起点' 和 '终点' 标记识别飞行段...")
    segments = []
    segment_start_index = -1
    for i, row in df.iterrows():
        marker = str(row[config.segment_marker_column])
        if '起点' in marker:
            segment_start_index = i
        elif '终点' in marker and segment_start_index != -1:
            segment_df = df.loc[segment_start_index:i].copy()
            segments.append(segment_df)
            segment_start_index = -1
            
    print(f"在文件 {file_path} 中识别出 {len(segments)} 个有效飞行段。")
    
    all_x, all_y, all_x_mark, all_y_mark = [], [], [], []
    segment_window_counts = [] 
    all_target_times = []

    for segment_df in segments:
        segment_indices = segment_df.index
        current_segment_features = scaled_features[segment_indices]
        current_segment_marks = time_marks[segment_indices]
        
        total_sequence_length = config.seq_len + config.pred_len
        
        if len(current_segment_features) < total_sequence_length:
            segment_window_counts.append(0)
            continue

        num_windows_in_segment = 0
        for i in range(len(current_segment_features) - total_sequence_length + 1):
            x_seq = current_segment_features[i : i + config.seq_len]
            x_mark_seq = current_segment_marks[i : i + config.seq_len]
            y_start_index = i + config.seq_len - config.label_len
            y_end_index = i + total_sequence_length
            
            y_seq_full = current_segment_features[y_start_index : y_end_index]
            y_seq_target = y_seq_full[:, target_cols_indices]
            
            y_mark_seq = current_segment_marks[y_start_index : y_end_index]

            all_x.append(x_seq)
            all_x_mark.append(x_mark_seq)
            all_y.append(y_seq_target)
            all_y_mark.append(y_mark_seq)
            
            target_original_index = segment_df.index[i + total_sequence_length - 1]
            all_target_times.append(df.loc[target_original_index, 'datetime'])
            
            num_windows_in_segment += 1
        
        segment_window_counts.append(num_windows_in_segment)
            
    total_windows = len(all_x)
    print(f"文件 {file_path} 处理完毕，共生成 {total_windows} 个有效窗口。")

    final_x = torch.tensor(np.array(all_x), dtype=torch.float32)
    final_y = torch.tensor(np.array(all_y), dtype=torch.float32)
    final_x_mark = torch.tensor(np.array(all_x_mark), dtype=torch.float32)
    final_y_mark = torch.tensor(np.array(all_y_mark), dtype=torch.float32)
    
    return final_x, final_y, final_x_mark, final_y_mark, segment_window_counts, all_target_times


def test_and_plot_predictions(model, loader, scaler, config, device, output_dir, feature_cols, segment_lengths=None, target_times=None):
    """
    此函数现在关注轨迹预测性能评估和可视化。
    """
    print(f"\n--- 开始在测试集上评估和预测目标: {config.target_feature} ---")
    model.eval()
    all_preds, all_trues = [], []
    with torch.no_grad():
        for i, (batch_x, batch_y, batch_x_mark, batch_y_mark) in enumerate(loader):
            batch_x, batch_y, batch_x_mark, batch_y_mark = \
                batch_x.to(device), batch_y.to(device), batch_x_mark.to(device), batch_y_mark.to(device)
            
            dec_inp = torch.zeros_like(batch_y[:, -config.pred_len:, :]).float()
            dec_inp = torch.cat([batch_y[:, :config.label_len, :], dec_inp], dim=1).float().to(device)
            
            outputs = model(batch_x, batch_x_mark, dec_inp, batch_y_mark)
            
            true_targets = batch_y[:, -config.pred_len:, :]
            pred_targets = outputs
            
            all_preds.append(pred_targets.detach().cpu().numpy())
            all_trues.append(true_targets.detach().cpu().numpy())

    preds = np.concatenate(all_preds, axis=0)
    trues = np.concatenate(all_trues, axis=0)

    if preds.ndim == 3:
        preds = preds[:, -1, :]
        trues = trues[:, -1, :]
        
    # --- <修改> 多变量反归一化处理 ---
    num_features = len(feature_cols)
    target_cols_indices = [feature_cols.index(t) for t in config.target_feature]
    
    dummy_preds = np.zeros((preds.shape[0], num_features))
    dummy_trues = np.zeros((trues.shape[0], num_features))

    dummy_preds[:, target_cols_indices] = preds
    dummy_trues[:, target_cols_indices] = trues

    preds_unscaled_full = scaler.inverse_transform(dummy_preds)
    trues_unscaled_full = scaler.inverse_transform(dummy_trues)

    feature_preds_dict = {feat: preds_unscaled_full[:, idx] for feat, idx in zip(config.target_feature, target_cols_indices)}
    feature_trues_dict = {feat: trues_unscaled_full[:, idx] for feat, idx in zip(config.target_feature, target_cols_indices)}

    # --- 计算每个目标的评估指标 ---
    metrics_data = []
    for feature in config.target_feature:
        r2 = r2_score(feature_trues_dict[feature], feature_preds_dict[feature])
        mse = mean_squared_error(feature_trues_dict[feature], feature_preds_dict[feature])
        mae = mean_absolute_error(feature_trues_dict[feature], feature_preds_dict[feature])
        print(f"测试评估 - {feature} - R2: {r2:.4f}, MSE: {mse:.6f}, MAE: {mae:.6f}")
        metrics_data.append({'Feature': feature, 'R2': r2, 'MSE': mse, 'MAE': mae})
    
    # --- 绘制分段预测轨迹图 ---
    valid_segments_to_plot = [l for l in segment_lengths if l > 0]
    n_segments = len(valid_segments_to_plot)

    if n_segments > 0:
        if n_segments == 1: n_cols = 1; figsize = (8, 8)
        else: n_cols = 2; figsize = (15, ((n_segments + 1) // 2) * 7)
        n_rows = (n_segments + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize, squeeze=False)
        fig.suptitle(f'TEST SET: Predicted vs Real Trajectory', fontsize=16)
        
        current_idx = 0
        plot_idx = 0
        
        lon_idx = feature_cols.index('Longitude')
        lat_idx = feature_cols.index('Latitude')

        for seg_idx in range(len(valid_segments_to_plot)):
            length = valid_segments_to_plot[seg_idx]
            if length == 0: continue
            
            segment_trues_lon = trues_unscaled_full[current_idx : current_idx + length, lon_idx]
            segment_preds_lon = preds_unscaled_full[current_idx : current_idx + length, lon_idx]
            segment_trues_lat = trues_unscaled_full[current_idx : current_idx + length, lat_idx]
            segment_preds_lat = preds_unscaled_full[current_idx : current_idx + length, lat_idx]
            
            row, col = plot_idx // n_cols, plot_idx % n_cols
            ax = axes[row, col]

            ax.plot(segment_trues_lon, segment_trues_lat, 'o-', label='Real Trajectory', markersize=3)
            ax.plot(segment_preds_lon, segment_preds_lat, 'x-', label='Predicted Trajectory', alpha=0.7, markersize=3)
            
            ax.plot(segment_trues_lon[0], segment_trues_lat[0], 'go', markersize=10, label='Start Point')
            
            ax.set_title(f"Test Segment {seg_idx+1}")
            ax.set_xlabel('Longitude')
            ax.set_ylabel('Latitude')
            ax.legend()
            ax.grid(True)
            ax.set_aspect('equal', adjustable='box')

            current_idx += length
            plot_idx += 1

        for j in range(plot_idx, n_rows * n_cols): fig.delaxes(axes.flatten()[j])
        plt.tight_layout(rect=[0, 0.03, 1, 0.95])
        save_path_img = os.path.join(output_dir, f'test_prediction_plot_trajectory.png')
        plt.savefig(save_path_img)
        print(f"\n预测轨迹图已保存到: {save_path_img}")
        plt.close(fig)

    # --- 保存评估指标和预测结果到CSV ---
    metrics_df = pd.DataFrame(metrics_data)
    metrics_save_path = os.path.join(output_dir, 'test_evaluation_metrics.csv')
    metrics_df.to_csv(metrics_save_path, index=False, encoding='utf-8-sig')
    print(f"评估指标已保存到: {metrics_save_path}")

    prediction_dfs = {'Time': [t.strftime('%H:%M:%S') for t in target_times]}
    for feature in config.target_feature:
        prediction_dfs[f'Real_{feature}'] = feature_trues_dict[feature]
        prediction_dfs[f'Predicted_{feature}'] = feature_preds_dict[feature]
    
    results_df = pd.DataFrame(prediction_dfs)
    results_save_path = os.path.join(output_dir, 'test_predictions.csv')
    results_df.to_csv(results_save_path, index=False, encoding='utf-8-sig')
    print(f"预测结果CSV已保存到: {results_save_path}")


if __name__ == '__main__':
    config = TrainingConfig() 

    seed = config.seed
    torch.manual_seed(seed)
    np.random.seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
        print(f"已为CPU和CUDA设置随机种子 {seed} 以确保测试的可复现性。")
    
    test_file_path = 'data/起落航线/test_set.csv'
    
    model_dir = config.results_dir
    test_results_dir = os.path.join(model_dir, 'test_results_trajectory')
    os.makedirs(test_results_dir, exist_ok=True)
    print(f"测试结果将保存到: '{test_results_dir}'")
    
    model_path = os.path.join(model_dir, f'best_model_{config.model_name}.pth')
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"未找到模型, 请确认路径正确: {model_path}")
    if not os.path.exists(test_file_path):
         raise FileNotFoundError(f"未找到测试集文件: {test_file_path}")

    # --- 加载训练集以拟合缩放器并获取特征列表 ---
    print("\n--- 在原始训练集上重新拟合缩放器和确定特征 ---")
    df_train_raw = pd.read_csv(config.train_file_path)
    
    feature_cols = config.use_features
    
    available_cols = df_train_raw.columns.tolist()
    missing_in_df = [col for col in feature_cols if col not in available_cols]
    if missing_in_df:
        print(f"警告: 在训练数据中找不到以下指定的特征: {missing_in_df}")
        feature_cols = [col for col in feature_cols if col not in missing_in_df]
        config.use_features = feature_cols

    if isinstance(config.target_feature, list):
        for t in config.target_feature:
            if t not in feature_cols:
                raise ValueError(f"致命错误: 目标特征 '{t}' 不在最终的 use_features 列表中。")
    elif config.target_feature not in feature_cols:
        raise ValueError(f"致命错误: 目标特征 '{config.target_feature}' 不在最终的 use_features 列表中。")

    scaler = MinMaxScaler()
    scaler.fit(df_train_raw[feature_cols])
    print(f"缩放器拟合成功，将使用以下 {len(feature_cols)} 个特征作为输入: {feature_cols}")
    print(f"测试目标为: {config.target_feature}")

    # --- 确保配置与模型匹配 ---
    config.data_dim = len(feature_cols)
    try:
        if isinstance(config.target_feature, str):
            config.target_dim_indices = [feature_cols.index(config.target_feature)]
        else:
            config.target_dim_indices = [feature_cols.index(t) for t in config.target_feature]
        print(f"目标特征 {config.target_feature} 在输入特征中的索引位置是: {config.target_dim_indices}")
    except ValueError as e:
        raise ValueError(f"致命错误: 目标特征 '{e.args[0]}' 没有在最终的特征列表 feature_cols 中找到。")


    # --- 加载已训练的模型 ---
    print("\n--- 加载已训练的模型 ---")
    informer_config_obj = config.to_informer_config()
    model = Informer.Model(informer_config_obj).to(device)
    
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.eval() 
    print(f"模型已从 {model_path} 加载")

    # --- 加载并处理测试数据 ---
    print("\n--- 处理测试数据 ---")
    test_x, test_y, test_x_mark, test_y_mark, test_segment_lengths, test_target_times = \
        load_and_process_segmented_data_for_test(test_file_path, config, scaler, feature_cols)

    test_dataset = TensorDataset(test_x, test_y, test_x_mark, test_y_mark)
    test_loader = DataLoader(test_dataset, batch_size=config.batch_size, shuffle=False)
    print("测试数据加载完毕。")

    # --- 在测试集上评估并可视化结果 ---
    test_and_plot_predictions(
        model=model,
        loader=test_loader,
        scaler=scaler,
        config=config,
        device=device,
        output_dir=test_results_dir,
        feature_cols=feature_cols,
        segment_lengths=test_segment_lengths,
        target_times=test_target_times,
    ) 