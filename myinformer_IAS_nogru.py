import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from torch.optim.lr_scheduler import ReduceLROnPlateau
import os
import shutil

from models import Informer_yuan as Informer
from utils.timefeatures import time_features

# --- 全局设置 ---
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use("ggplot")
print(f"PyTorch Version: {torch.__version__}")


# =================================================================================================
# 1. 配置参数模块 (Configuration)
# =================================================================================================
class TrainingConfig:
    def __init__(self):
        # --- 核心目标 ---
        self.target_feature = 'IAS' # <--- 新增: 指定单一预测目标
        self.target_dim_index = None # <--- 新增: 目标特征的索引，将动态计算

        # --- 新增: 特征选择 ---
        # 基于Kendall相关性分析选出的与IAS最相关的特征 (Top 10正 + Top 5负)
        self.use_features = [
            'TAS', 'E1 RPM', 'GndSpd', 'E1 OilP', 'NormAc', 'VSpd', 'OAT', 
            'WndSpd', 'WndDr', 'E1 FFlow', 'TRK', 'FQtyL', 'Altitude', 
            'E1 OilT', 'Pitch',
            'IAS'
        ]

        # --- 文件路径与数据列名 ---
        self.train_file_path = 'data/爬升/train_set.csv'
        self.validation_file_path = 'data/爬升/validation_set.csv'
        self.results_dir = f'myinformer_results_{self.target_feature}_nogru_prediction_7_23no' # <--- 修改: 新的结果目录
        self.segment_marker_column = '段标记'
        self.time_column = 'Time'
        self.exclude_features = ['Latitude', 'Longitude','source_file','trk_rate'] # 注意: 此参数在新逻辑下不再直接用于选择特征

        # --- 数据处理参数 ---
        self.freq = 's'

        # --- 模型输入输出维度 ---
        self.seq_len = 10
        self.label_len = 2
        self.pred_len = 1
        self.c_out = 1  # <--- 修改: 输出维度固定为1

        # --- 训练过程参数 ---
        self.batch_size = 64
        self.num_epochs = 50
        self.learning_rate = 0.00005
        self.early_patience_ratio = 0.2
        self.lr_scheduler_patience = 5

        # --- Informer模型核心参数 ---
        self.data_dim = None # 输入特征维度, 动态设置
        self.d_model = 512
        self.n_heads = 8
        self.e_layers = 2
        self.d_layers = 1
        self.d_ff = 2048
        self.dropout = 0.3
        self.activation = 'gelu'
        self.distil = True
        self.channel_independence = 0

        # --- 固定参数 ---
        self.embed = 'timeF'
        self.output_attention = False
        self.factor = 5
        self.task_name = 'short_term_forecast'
        
    def to_informer_config(self):
        informer_args = {
            'enc_in': self.data_dim,
            'dec_in': self.c_out, # <--- 修改: 解码器输入维度也是1
            'c_out': self.c_out,
            'seq_len': self.seq_len,
            'label_len': self.label_len,
            'pred_len': self.pred_len,
            'd_model': self.d_model,
            'n_heads': self.n_heads,
            'e_layers': self.e_layers,
            'd_layers': self.d_layers,
            'd_ff': self.d_ff,
            'factor': self.factor,
            'dropout': self.dropout,
            'attn': 'prob',
            'embed': self.embed,
            'freq': self.freq,
            'activation': self.activation,
            'output_attention': self.output_attention,
            'distil': self.distil,
            'task_name': self.task_name,
            'target_dim_index': self.target_dim_index, # <--- 新增: 将索引传递给模型配置
            'device': torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        }
        
        return Namespace(**informer_args)

class Namespace:
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)

# =================================================================================================
# 2. 数据处理模块 (Data Handling)
# =================================================================================================
def load_and_process_segmented_data(file_path, config, scaler, feature_cols):
    print(f"--- 开始处理文件: {file_path} ---")
    df = pd.read_csv(file_path)
    df['datetime'] = pd.to_datetime(df[config.time_column], format='%H:%M:%S')

    df_stamp = df[['datetime']].copy()
    df_stamp.rename(columns={'datetime': 'date'}, inplace=True)
    time_marks = time_features(df_stamp, timeenc=1, freq=config.freq)

    df_features = df[feature_cols]
    scaled_features = scaler.transform(df_features)
    
    # <--- 新增: 获取目标列的索引 ---
    try:
        target_col_index = feature_cols.index(config.target_feature)
    except ValueError:
        raise ValueError(f"错误: 目标特征 '{config.target_feature}' 不在特征列表_cols中。")

    segments = []
    segment_start_index = -1
    for i, row in df.iterrows():
        marker = str(row[config.segment_marker_column])
        if '起点' in marker:
            segment_start_index = i
        elif '终点' in marker and segment_start_index != -1:
            segments.append(df.loc[segment_start_index:i])
            segment_start_index = -1
            
    all_x, all_y, all_x_mark, all_y_mark = [], [], [], []
    segment_window_counts = []
    all_target_times = []
    
    for segment_df in segments:
        segment_indices = segment_df.index
        current_segment_features = scaled_features[segment_indices]
        current_segment_marks = time_marks[segment_indices]
        
        total_sequence_length = config.seq_len + config.pred_len
        if len(current_segment_features) < total_sequence_length:
            segment_window_counts.append(0)
            continue

        num_windows_in_segment = 0
        for i in range(len(current_segment_features) - total_sequence_length + 1):
            x_seq = current_segment_features[i : i + config.seq_len]
            x_mark_seq = current_segment_marks[i : i + config.seq_len]
            
            y_start_index = i + config.seq_len - config.label_len
            y_end_index = i + total_sequence_length
            
            # <--- 修改: y_seq只提取目标特征那一列 ---
            y_seq_full = current_segment_features[y_start_index : y_end_index]
            y_seq_target = y_seq_full[:, target_col_index:target_col_index+1]
            
            y_mark_seq = current_segment_marks[y_start_index : y_end_index]

            all_x.append(x_seq)
            all_x_mark.append(x_mark_seq)
            all_y.append(y_seq_target) # <--- 使用只包含目标特征的y
            all_y_mark.append(y_mark_seq)
            
            target_original_index = segment_df.index[i + total_sequence_length - 1]
            all_target_times.append(df.loc[target_original_index, 'datetime'])
            num_windows_in_segment += 1
        
        segment_window_counts.append(num_windows_in_segment)
            
    total_windows = len(all_x)
    print(f"文件 {file_path} 处理完毕，共生成 {total_windows} 个有效窗口。")

    final_x = torch.tensor(np.array(all_x), dtype=torch.float32)
    final_y = torch.tensor(np.array(all_y), dtype=torch.float32)
    final_x_mark = torch.tensor(np.array(all_x_mark), dtype=torch.float32)
    final_y_mark = torch.tensor(np.array(all_y_mark), dtype=torch.float32)
    
    return final_x, final_y, final_x_mark, final_y_mark, segment_window_counts, all_target_times

# =================================================================================================
# 3. 模型训练模块 (Model Training)
# =================================================================================================
def model_train_val(net, config, train_loader, val_loader, optimizer, criterion, scheduler, device):
    """训练和验证模型，并包含早停机制。"""
    train_losses, val_losses = [], []
    best_val_loss = float('inf')
    early_stop_counter = 0
    early_patience_epochs = int(config.num_epochs * config.early_patience_ratio)
    final_epoch = 0

    print("\n--- 开始模型训练 ---")
    for epoch in range(config.num_epochs):
        final_epoch = epoch + 1
        net.train()
        total_train_loss = 0
        for i, (batch_x, batch_y, batch_x_mark, batch_y_mark) in enumerate(train_loader):
            batch_x, batch_y, batch_x_mark, batch_y_mark = \
                batch_x.to(device), batch_y.to(device), batch_x_mark.to(device), batch_y_mark.to(device)

            optimizer.zero_grad()
            
            # <--- 修改: 解码器输入现在也是单变量 ---
            dec_inp = torch.zeros_like(batch_y[:, -config.pred_len:, :]).float()
            dec_inp = torch.cat([batch_y[:, :config.label_len, :], dec_inp], dim=1).float().to(device)

            outputs = net(batch_x, batch_x_mark, dec_inp, batch_y_mark)
            
            true_targets = batch_y[:, -config.pred_len:, :]
            # 从多变量输出中选择单一目标变量的预测结果
            pred_targets = outputs[:, :, config.target_dim_index:config.target_dim_index+1]
            
            loss = criterion(pred_targets, true_targets)
            total_train_loss += loss.item()
            
            loss.backward()
            optimizer.step()
        
        avg_train_loss = total_train_loss / len(train_loader)
        train_losses.append(avg_train_loss)

        net.eval()
        total_val_loss = 0
        with torch.no_grad():
            for i, (batch_x, batch_y, batch_x_mark, batch_y_mark) in enumerate(val_loader):
                batch_x, batch_y, batch_x_mark, batch_y_mark = \
                    batch_x.to(device), batch_y.to(device), batch_x_mark.to(device), batch_y_mark.to(device)
                
                dec_inp = torch.zeros_like(batch_y[:, -config.pred_len:, :]).float()
                dec_inp = torch.cat([batch_y[:, :config.label_len, :], dec_inp], dim=1).float().to(device)

                outputs = net(batch_x, batch_x_mark, dec_inp, batch_y_mark)
                
                true_targets = batch_y[:, -config.pred_len:, :]
                # 从多变量输出中选择单一目标变量的预测结果
                pred_targets = outputs[:, :, config.target_dim_index:config.target_dim_index+1]
                
                loss = criterion(pred_targets, true_targets)
                total_val_loss += loss.item()

        avg_val_loss = total_val_loss / len(val_loader)
        val_losses.append(avg_val_loss)
        scheduler.step(avg_val_loss)
        
        current_lr = optimizer.param_groups[0]['lr']
        print(f"Epoch: {epoch+1}/{config.num_epochs}, Train Loss: {avg_train_loss:.5f}, Val Loss: {avg_val_loss:.5f}, Current LR: {current_lr}")

        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            save_path = os.path.join(config.results_dir, f'best_model_{config.target_feature}.pth')
            torch.save(net.state_dict(), save_path)
            print(f"Validation loss decreased. Saving best model to {save_path}.")
            early_stop_counter = 0
        else:
            early_stop_counter += 1
            if early_stop_counter >= early_patience_epochs:
                print(f"Early stopping triggered after {early_patience_epochs} epochs without improvement.")
                break
    
    print("--- 训练结束 ---")
    load_path = os.path.join(config.results_dir, f'best_model_{config.target_feature}.pth')
    net.load_state_dict(torch.load(load_path))
    return net, train_losses, val_losses, final_epoch

# =================================================================================================
# 4. 评估与可视化模块 (Evaluation & Visualization)
# =================================================================================================
def plot_loss_curves(train_loss, val_loss, final_epoch, config):
    plt.figure(figsize=(12, 6))
    plt.plot(range(1, final_epoch + 1), train_loss, label='Training Loss')
    plt.plot(range(1, final_epoch + 1), val_loss, label='Validation Loss')
    plt.title(f'Training and Validation Loss ({config.target_feature})')
    plt.xlabel('Epochs')
    plt.ylabel('Loss (MSE)')
    plt.legend()
    plt.grid(True)
    save_path = os.path.join(config.results_dir, 'loss_curves.png')
    plt.savefig(save_path)
    print(f"损失曲线图已保存到: {save_path}")
    plt.close()

def evaluate_and_plot_predictions(model, loader, scaler, config, device, feature_cols, segment_lengths=None, target_times=None):
    print(f"\n--- 开始在验证集上评估和预测目标: {config.target_feature} ---")
    model.eval()
    all_preds, all_trues = [], []
    with torch.no_grad():
        for i, (batch_x, batch_y, batch_x_mark, batch_y_mark) in enumerate(loader):
            batch_x, batch_y, batch_x_mark, batch_y_mark = \
                batch_x.to(device), batch_y.to(device), batch_x_mark.to(device), batch_y_mark.to(device)
            
            dec_inp = torch.zeros_like(batch_y[:, -config.pred_len:, :]).float()
            dec_inp = torch.cat([batch_y[:, :config.label_len, :], dec_inp], dim=1).float().to(device)
            
            outputs = model(batch_x, batch_x_mark, dec_inp, batch_y_mark)
            
            true_targets = batch_y[:, -config.pred_len:, :]
            pred_targets = outputs
            
            all_preds.append(pred_targets.detach().cpu().numpy())
            all_trues.append(true_targets.detach().cpu().numpy())

    preds = np.concatenate(all_preds, axis=0)
    trues = np.concatenate(all_trues, axis=0)

    if preds.ndim == 3:
        preds = preds[:, -1, :]
        trues = trues[:, -1, :]
        
    # --- <修改> 单变量反归一化特殊处理 ---
    # 1. 创建一个与原始数据维度相同的 "哑" (dummy) 数组
    num_features = len(feature_cols)
    target_col_index = feature_cols.index(config.target_feature)
    
    dummy_preds = np.zeros((preds.shape[0], num_features))
    dummy_trues = np.zeros((trues.shape[0], num_features))

    # 2. 将我们的单变量预测结果和真实值填充到哑数组的正确列
    dummy_preds[:, target_col_index] = preds[:, target_col_index]
    dummy_trues[:, target_col_index] = trues.squeeze()

    # 3. 对完整的哑数组进行反归一化
    preds_unscaled_full = scaler.inverse_transform(dummy_preds)
    trues_unscaled_full = scaler.inverse_transform(dummy_trues)

    # 4. 从反归一化后的数组中，再次提取出我们的目标列
    feature_preds = preds_unscaled_full[:, target_col_index]
    feature_trues = trues_unscaled_full[:, target_col_index]

    # --- 计算评估指标 ---
    r2 = r2_score(feature_trues, feature_preds)
    mse = mean_squared_error(feature_trues, feature_preds)
    mae = mean_absolute_error(feature_trues, feature_preds)
    print(f"评估结果 - R2: {r2:.4f}, MSE: {mse:.4f}, MAE: {mae:.4f}")
    
    metrics_data = [{'Feature': config.target_feature, 'R2': r2, 'MSE': mse, 'MAE': mae}]
    
    # --- 绘制分段预测结果 ---
    valid_segments_to_plot = [l for l in segment_lengths if l > 0]
    n_segments = len(valid_segments_to_plot)

    if n_segments > 0:
        if n_segments == 1: n_cols = 1; figsize = (12, 5)
        else: n_cols = 2; figsize = (15, ((n_segments + 1) // 2) * 4)
        n_rows = (n_segments + n_cols - 1) // n_cols

        fig, axes = plt.subplots(n_rows, n_cols, figsize=figsize, squeeze=False)
        fig.suptitle(f'Prediction vs Real ({config.target_feature})', fontsize=16)
        
        current_idx = 0
        plot_idx = 0
        for seg_idx in range(len(valid_segments_to_plot)):
            length = valid_segments_to_plot[seg_idx]
            if length == 0: continue
            
            segment_trues = feature_trues[current_idx : current_idx + length]
            segment_preds = feature_preds[current_idx : current_idx + length]
            segment_times = target_times[current_idx : current_idx + length]
            
            row, col = plot_idx // n_cols, plot_idx % n_cols
            ax = axes[row, col]

            ax.plot(segment_times, segment_trues, label='Real Value')
            ax.plot(segment_times, segment_preds, label='Predicted Value', alpha=0.7)
            ax.set_title(f"Segment {seg_idx+1}")
            ax.set_ylabel(config.target_feature)
            ax.legend()
            ax.grid(True)
            
            import matplotlib.dates as mdates
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
            plt.setp(ax.get_xticklabels(), rotation=30, ha="right")

            current_idx += length
            plot_idx += 1

        for j in range(plot_idx, n_rows * n_cols): fig.delaxes(axes.flatten()[j])
        plt.tight_layout(rect=[0, 0.03, 1, 0.95])
        save_path_img = os.path.join(config.results_dir, 'prediction_plot.png')
        plt.savefig(save_path_img)
        print(f"预测结果图已保存到: {save_path_img}")
        plt.close(fig)

    # --- 保存评估指标和预测结果到CSV ---
    metrics_df = pd.DataFrame(metrics_data)
    metrics_save_path = os.path.join(config.results_dir, 'evaluation_metrics.csv')
    metrics_df.to_csv(metrics_save_path, index=False, encoding='utf-8-sig')
    print(f"\n评估指标已保存到: {metrics_save_path}")

    prediction_dfs = {'Time': [t.strftime('%H:%M:%S') for t in target_times],
                      f'Real_{config.target_feature}': feature_trues,
                      f'Predicted_{config.target_feature}': feature_preds}
    results_df = pd.DataFrame(prediction_dfs)
    results_save_path = os.path.join(config.results_dir, 'predictions.csv')
    results_df.to_csv(results_save_path, index=False, encoding='utf-8-sig')
    print(f"预测结果CSV已保存到: {results_save_path}")

# =================================================================================================
# 5. 主程序入口 (Main)
# =================================================================================================
def setup_results_directory(config):
    dir_path = config.results_dir
    if os.path.exists(dir_path):
        shutil.rmtree(dir_path)
        print(f"已清空旧的结果目录: {dir_path}")
    os.makedirs(dir_path)
    print(f"已创建新的结果目录: {dir_path}")

if __name__ == '__main__':
    config = TrainingConfig()
    setup_results_directory(config)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    df_train_raw = pd.read_csv(config.train_file_path)
    
    # --- <修改> 使用在配置中指定的特征列表 ---
    feature_cols = config.use_features
    
    # 安全检查：确保所有指定的特征都存在于DataFrame中
    missing_in_df = [col for col in feature_cols if col not in df_train_raw.columns]
    if missing_in_df:
        raise ValueError(f"致命错误: 在训练数据中找不到以下指定的特征: {missing_in_df}")

    # 安全检查：确保目标特征在所选特征列表中
    if config.target_feature not in feature_cols:
        raise ValueError(f"致命错误: 目标特征 '{config.target_feature}' 不在指定的 use_features 列表中。")
    
    config.data_dim = len(feature_cols)

    # --- 新增: 计算并设置目标特征的索引 ---
    try:
        config.target_dim_index = feature_cols.index(config.target_feature)
        print(f"目标特征 '{config.target_feature}' 在输入特征中的索引位置是: {config.target_dim_index}")
    except ValueError:
        raise ValueError(f"致命错误: 目标特征 '{config.target_feature}' 没有在最终的特征列表 feature_cols 中找到。")

    print(f"模型将使用 {config.data_dim} 个输入特征: {feature_cols}")
    print(f"模型将预测 {config.c_out} 个输出特征: ['{config.target_feature}']")

    scaler = MinMaxScaler()
    scaler.fit(df_train_raw[feature_cols])
    print("数据缩放器 (Scaler) 已在训练集上完成拟合。")

    train_x, train_y, train_x_mark, train_y_mark, _, _ = load_and_process_segmented_data(config.train_file_path, config, scaler, feature_cols)
    val_x, val_y, val_x_mark, val_y_mark, val_segment_lengths, val_target_times = load_and_process_segmented_data(config.validation_file_path, config, scaler, feature_cols)

    train_dataset = TensorDataset(train_x, train_y, train_x_mark, train_y_mark)
    train_loader = DataLoader(train_dataset, batch_size=config.batch_size, shuffle=True)
    val_dataset = TensorDataset(val_x, val_y, val_x_mark, val_y_mark)
    val_loader = DataLoader(val_dataset, batch_size=config.batch_size, shuffle=False)
    print("\nDataLoader 创建完毕。")

    informer_config_obj = config.to_informer_config()
    model = Informer.Model(informer_config_obj).to(device)
    criterion = nn.MSELoss().to(device)
    optimizer = optim.Adam(model.parameters(), lr=config.learning_rate, weight_decay=1e-5)
    scheduler = ReduceLROnPlateau(optimizer, 'min', patience=config.lr_scheduler_patience)
    print("模型、损失函数和优化器初始化完毕。")
    
    trained_model, train_loss, val_loss, final_epoch = model_train_val(
        net=model, config=config, train_loader=train_loader, val_loader=val_loader,
        optimizer=optimizer, criterion=criterion, scheduler=scheduler, device=device
    )

    plot_loss_curves(train_loss, val_loss, final_epoch, config)

    evaluate_and_plot_predictions(
        model=trained_model, loader=val_loader, scaler=scaler, config=config,
        device=device, feature_cols=feature_cols, segment_lengths=val_segment_lengths,
        target_times=val_target_times
    ) 