import torch
import torch.nn as nn
import torch.nn.functional as F


class ConvLayer(nn.Module):
    """
    Informer中特有的"卷积蒸馏"(Convolutional Distillation)层。
    它的核心思想是,在每个Encoder层之后,使用一个卷积层来对序列进行下采样(down-sampling),
    从而缩短序列长度,减少后续层的计算量和内存占用,这被称为"自注意力蒸馏"。
    """
    def __init__(self, c_in):
        """
        构造函数。
        参数:
            c_in (int): 输入和输出的通道数(channels)。在Informer中,这通常等于 d_model。
        """
        super(ConvLayer, self).__init__()
        # 使用一维卷积层对序列进行变换。
        # kernel_size=3: 卷积核大小为3,意味着每个输出点会考虑其邻近的3个输入点。
        # padding=2, padding_mode='circular': 使用循环填充,大小为2。这是为了在卷积后能更好地保持序列的边缘信息。
        self.downConv = nn.Conv1d(in_channels=c_in,
                                  out_channels=c_in,
                                  kernel_size=3,
                                  padding=2,
                                  padding_mode='circular')
        # BatchNorm1d: 对卷积后的输出进行批归一化,稳定训练。
        self.norm = nn.BatchNorm1d(c_in)
        # ELU: 激活函数,Exponential Linear Unit。
        self.activation = nn.ELU()
        # MaxPool1d: 最大池化层,这是实现序列长度减半的核心。
        # stride=2: 步长为2,意味着池化窗口每次移动2个位置,从而使输出序列的长度大约为输入的一半。
        self.maxPool = nn.MaxPool1d(kernel_size=3, stride=2, padding=1)

    def forward(self, x):
        """
        前向传播。
        参数:
            x (Tensor): 输入张量,形状为 [B, L, D], 即 [批量, 序列长度, 特征维度]。
        返回:
            Tensor: 经过蒸馏（下采样）后的张量,形状约为 [B, L/2, D]。
        """
        # 1. x.permute(0, 2, 1): 交换维度1和2,将形状从 [B, L, D] 变为 [B, D, L]。
        #    这是因为 PyTorch 的 Conv1d 和 MaxPool1d期望的输入格式是 (批量, 通道数, 序列长度)。
        x = self.downConv(x.permute(0, 2, 1))
        x = self.norm(x)
        x = self.activation(x)
        x = self.maxPool(x)
        # 2. x.transpose(1, 2): 将维度1和2交换回来,形状从 [B, D, L/2] 变回 [B, L/2, D],
        #    以匹配 Transformer 其他部分的标准输入格式。
        x = x.transpose(1, 2)
        return x


class EncoderLayer(nn.Module):
    """
    构成编码器(Encoder)的单个基础层。
    一个 EncoderLayer 包含两个主要部分：
    1. 一个多头自注意力模块 (Multi-Head Self-Attention)。
    2. 一个位置前馈网络 (Position-wise Feed-Forward Network)。
    每个部分后面都跟随着一个残差连接(Residual Connection)和层归一化(Layer Normalization)。
    """
    def __init__(self, attention, d_model, d_ff=None, dropout=0.1, activation="relu"):
        """
        构造函数。
        参数:
            attention (nn.Module): 传入的已经实例化的注意力模块 (例如 AttentionLayer)。
            d_model (int): 模型的主维度。
            d_ff (int, optional): 前馈网络的隐藏层维度。如果为None,则默认为 4 * d_model。
            dropout (float, optional): Dropout 概率。
            activation (str, optional): 前馈网络中使用的激活函数,"relu" 或 "gelu"。
        """
        super(EncoderLayer, self).__init__()
        d_ff = d_ff or 4 * d_model
        self.attention = attention
        # 前馈网络(FFN)通常由两个线性层组成。这里用两个1x1的卷积层来实现,是一种常见的优化技巧,
        # 因为对于序列数据,1x1的卷积等效于一个作用于每个时间步的线性层,且在某些硬件上可能更高效。
        self.conv1 = nn.Conv1d(in_channels=d_model, out_channels=d_ff, kernel_size=1)
        self.conv2 = nn.Conv1d(in_channels=d_ff, out_channels=d_model, kernel_size=1)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        self.activation = F.relu if activation == "relu" else F.gelu

    def forward(self, x, attn_mask=None, tau=None, delta=None):
        """
        前向传播。
        参数:
            x (Tensor): 输入张量,形状为 [B, L, D]。
            attn_mask (Tensor, optional): 注意力掩码。
            tau, delta (Tensor, optional): De-stationary attention 中使用的参数。
        返回:
            (Tensor, Tensor): 返回处理后的输出张量和注意力权重。
        """
        # 1. --- 自注意力模块 ---
        #    将 x 同时作为 Q, K, V 输入到注意力模块。
        new_x, attn = self.attention(
            x, x, x,
            attn_mask=attn_mask,
            tau=tau, delta=delta
        )
        # 1.1 残差连接: 将注意力模块的输出与原始输入 x 相加。
        #     x + dropout(new_x) 是 Transformer 的核心结构之一,有助于防止梯度消失。
        x = x + self.dropout(new_x)
        # 1.2 层归一化: 对残差连接后的结果进行归一化。
        y = x = self.norm1(x)
        
        # 2. --- 前馈网络模块 ---
        # 2.1 y.transpose(-1, 1): 同样,为了匹配 Conv1d 的输入格式,需要交换最后两个维度。
        y = self.dropout(self.activation(self.conv1(y.transpose(-1, 1))))
        y = self.dropout(self.conv2(y).transpose(-1, 1))
        # 2.2 残差连接和层归一化
        return self.norm2(x + y), attn


class Encoder(nn.Module):
    """
    完整的编码器模块,由多个 EncoderLayer 和可选的 ConvLayer 堆叠而成。
    """
    def __init__(self, attn_layers, conv_layers=None, norm_layer=None):
        """
        构造函数。
        参数:
            attn_layers (list): 一个包含多个已实例化的 EncoderLayer 的列表。
            conv_layers (list, optional): 一个包含多个已实例化的 ConvLayer 的列表 (用于蒸馏)。
            norm_layer (nn.Module, optional): 在所有层之后使用的最终归一化层。
        """
        super(Encoder, self).__init__()
        # nn.ModuleList 用于包装一个包含 nn.Module 的 Python 列表,
        # 这样PyTorch才能正确地注册和管理这些子模块。
        self.attn_layers = nn.ModuleList(attn_layers)
        self.conv_layers = nn.ModuleList(conv_layers) if conv_layers is not None else None
        self.norm = norm_layer

    def forward(self, x, attn_mask=None, tau=None, delta=None):
        """
        前向传播。
        参数:
            x (Tensor): 输入张量,形状为 [B, L, D]。
            attn_mask (Tensor, optional): 注意力掩码。
        返回:
            (Tensor, list): 返回编码器的最终输出张量和每一层的注意力权重列表。
        """
        attns = []
        if self.conv_layers is not None:
            # 如果使用蒸馏,则交替执行 EncoderLayer 和 ConvLayer
            for i, (attn_layer, conv_layer) in enumerate(zip(self.attn_layers, self.conv_layers)):
                delta = delta if i == 0 else None
                x, attn = attn_layer(x, attn_mask=attn_mask, tau=tau, delta=delta)
                x = conv_layer(x) # 蒸馏,缩短序列长度
                attns.append(attn)
            # 最后一层 EncoderLayer 单独执行,其后没有蒸馏层。
            x, attn = self.attn_layers[-1](x, tau=tau, delta=None)
            attns.append(attn)
        else:
            # 如果不使用蒸馏,则顺序执行所有 EncoderLayer
            for attn_layer in self.attn_layers:
                x, attn = attn_layer(x, attn_mask=attn_mask, tau=tau, delta=delta)
                attns.append(attn)

        # 对最终的输出进行归一化
        if self.norm is not None:
            x = self.norm(x)

        return x, attns


class DecoderLayer(nn.Module):
    """
    构成解码器(Decoder)的单个基础层。
    一个 DecoderLayer 包含三个主要部分：
    1. 一个带掩码的多头自注意力模块 (Masked Multi-Head Self-Attention)。
    2. 一个多头交叉注意力模块 (Multi-Head Cross-Attention),用于关注编码器的输出。
    3. 一个位置前馈网络 (Position-wise Feed-Forward Network)。
    """
    def __init__(self, self_attention, cross_attention, d_model, d_ff=None,
                 dropout=0.1, activation="relu"):
        """
        构造函数。
        参数:
            self_attention (nn.Module): 自注意力模块。
            cross_attention (nn.Module): 交叉注意力模块。
            d_model, d_ff, dropout, activation: 与 EncoderLayer 中的参数相同。
        """
        super(DecoderLayer, self).__init__()
        d_ff = d_ff or 4 * d_model
        self.self_attention = self_attention
        self.cross_attention = cross_attention
        self.conv1 = nn.Conv1d(in_channels=d_model, out_channels=d_ff, kernel_size=1)
        self.conv2 = nn.Conv1d(in_channels=d_ff, out_channels=d_model, kernel_size=1)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        self.activation = F.relu if activation == "relu" else F.gelu

    def forward(self, x, cross, x_mask=None, cross_mask=None, tau=None, delta=None):
        """
        前向传播。
        参数:
            x (Tensor): 解码器的输入,来自上一层解码器,形状 [B, L_dec, D]。
            cross (Tensor): 编码器的输出,用作交叉注意力的 K 和 V,形状 [B, L_enc, D]。
            x_mask (Tensor, optional): 自注意力的掩码 (通常是下三角掩码)。
            cross_mask (Tensor, optional): 交叉注意力的掩码 (通常是 padding 掩码)。
        返回:
            Tensor: 解码器层的输出张量。
        """
        # 1. --- 带掩码的自注意力 ---
        #    x 作为 Q, K, V。x_mask 会确保每个位置只能注意到它前面的位置。

        """  # 接收最开始的输入
        input_to_self_attn = x 
        # 计算自注意力输出
        output_from_self_attn = self.self_attention(input_to_self_attn, input_to_self_attn, input_to_self_attn, ...)[0]
        # --- 第一个 "Add & Norm" ---
        # 执行残差连接 (Add) 和层归一化 (Norm)
        x_after_self_attn = self.norm1(input_to_self_attn + self.dropout(output_from_self_attn)) 
        """
        
        x = x + self.dropout(self.self_attention(
            x, x, x,
            attn_mask=x_mask,
            tau=tau, delta=None
        )[0]) # [0] 是因为注意力层会返回 (输出, 注意力权重),这里我们只需要输出。
        x = self.norm1(x)

        # 2. --- 交叉注意力 ---
        #    这是解码器-编码器架构的核心。
        #    查询 Q 来自解码器 (x),而键 K 和值 V 来自编码器的输出 (cross)。
        #    这允许解码器的每个位置都能"查看"整个输入序列的信息。

        """  # 接收上一个模块的输出
        input_to_cross_attn = x_after_self_attn
        # 计算交叉注意力输出
        output_from_cross_attn = self.cross_attention(input_to_cross_attn, cross, cross, ...)[0]
        # --- 第二个 "Add & Norm" ---
        # 执行残差连接 (Add) 和层归一化 (Norm)
        x_after_cross_attn = self.norm2(input_to_cross_attn + self.dropout(output_from_cross_attn))
        """
        x = x + self.dropout(self.cross_attention(
            x, cross, cross,
            attn_mask=cross_mask,
            tau=tau, delta=delta
        )[0])
        y = x = self.norm2(x)
        
        # 3. --- 前馈网络 ---
        #    与 EncoderLayer 中的结构相同。
        y = self.dropout(self.activation(self.conv1(y.transpose(-1, 1))))
        y = self.dropout(self.conv2(y).transpose(-1, 1))
        
        return self.norm3(x + y)


class Decoder(nn.Module):
    """
    完整的解码器模块,由多个 DecoderLayer 堆叠而成。
    """
    def __init__(self, layers, norm_layer=None, projection=None):
        """
        构造函数。
        参数:
            layers (list): 一个包含多个已实例化的 DecoderLayer 的列表。
            norm_layer (nn.Module, optional): 最终的归一化层。
            projection (nn.Module, optional): 最终的投影层,用于将输出映射到目标维度。
        """
        super(Decoder, self).__init__()
        self.layers = nn.ModuleList(layers)
        self.norm = norm_layer
        self.projection = projection

    def forward(self, x, cross, x_mask=None, cross_mask=None, tau=None, delta=None):
        """
        前向传播。
        参数:
            x (Tensor): 目标序列的嵌入,形状 [B, L_dec, D]。
            cross (Tensor): 编码器的输出,形状 [B, L_enc, D]。
            x_mask, cross_mask: 对应的掩码。
        返回:
            Tensor: 解码器的最终输出。
        """
        # 依次通过每一个 DecoderLayer。
        # 注意每一层都需要接收解码器自身的输入(x)和编码器的输出(cross)。
        for layer in self.layers:
            x = layer(x, cross, x_mask=x_mask, cross_mask=cross_mask, tau=tau, delta=delta)

        # 最终的归一化
        if self.norm is not None:
            x = self.norm(x)

        # 最终的投影,得到预测结果
        if self.projection is not None:
            x = self.projection(x)
        return x
