import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import Button, RadioButtons, TextBox, Cursor
from pathlib import Path
import tkinter as tk
from tkinter import filedialog
from matplotlib.widgets import SpanSelector
from mpl_toolkits.mplot3d import Axes3D
import matplotlib
from matplotlib.lines import Line2D

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong', 'Arial Unicode MS']  # 优先使用的中文字体，按顺序尝试
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
plt.rcParams['font.family'] = 'sans-serif'  # 使用无衬线字体

# 检查系统中文字体可用性
def check_chinese_font():
    import matplotlib.font_manager as fm
    
    # 检查可用的中文字体
    chinese_fonts = []
    all_fonts = set([f.name for f in fm.fontManager.ttflist])
    
    for f in ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi', 'FangSong', 'Arial Unicode MS', 'WenQuanYi Micro Hei']:
        if f in all_fonts:
            chinese_fonts.append(f)
    
    if chinese_fonts:
        # 使用第一个可用的中文字体
        plt.rcParams['font.sans-serif'].insert(0, chinese_fonts[0])
        print(f"使用中文字体: {chinese_fonts[0]}")
    else:
        print("警告: 未找到支持中文的字体，界面可能无法正确显示中文")

# 启动时检查中文字体
check_chinese_font()

class FlightActionMarker:
    def __init__(self):
        # 初始化变量
        self.df = None
        self.file_path = None
        self.file_name = None
        self.action_segments = []
        self.current_action = "平飞"  # 默认动作
        self.action_colors = {
            "爬升": "green",
            "平飞": "gray",
            "转弯": "red",
            "下降": "orange",
            "DME弧": "purple",
            "其他": "blue"
        }
        self.action_codes = {
            "爬升": "A",
            "平飞": "B",
            "转弯": "C",
            "下降": "D", 
            "DME弧": "E",
            "其他": "F"
        }
        # 姿态数据窗口
        self.data_window = None
        self.cursors = {}
        self.data_display_text = None
        self.current_index = 0
        self.span_start_index = -1
        # 创建图形界面
        self.setup_ui()
    
    def setup_ui(self):
        # 创建主窗口
        self.fig = plt.figure(figsize=(15, 10))
        self.fig.canvas.manager.set_window_title('飞行动作手动标记工具')
        
        # 设置子图布局
        gs = self.fig.add_gridspec(3, 3)
        self.ax_3d = self.fig.add_subplot(gs[0:2, 0:2], projection='3d')
        self.ax_2d = self.fig.add_subplot(gs[0:2, 2])
        self.ax_timeline = self.fig.add_subplot(gs[2, :])
        
        # 按钮区域 - 位于底部
        button_height = 0.05
        button_width = 0.12
        button_spacing = 0.02
        button_bottom = 0.01
        
        # 载入数据按钮
        self.ax_buttons = plt.axes([0.05, button_bottom, button_width, button_height])
        self.btn_load = Button(self.ax_buttons, '载入数据')
        self.btn_load.on_clicked(self.load_data)
        
        # 保存结果按钮
        self.ax_save = plt.axes([0.05 + button_width + button_spacing, button_bottom, button_width, button_height])
        self.btn_save = Button(self.ax_save, '保存结果')
        self.btn_save.on_clicked(self.save_results)
        
        # 清除所有标记按钮
        self.ax_clear = plt.axes([0.05 + (button_width + button_spacing) * 2, button_bottom, button_width, button_height])
        self.btn_clear = Button(self.ax_clear, '清除所有标记')
        self.btn_clear.on_clicked(self.clear_marks)
        
        # 删除最后标记按钮
        self.ax_delete_last = plt.axes([0.05 + (button_width + button_spacing) * 3, button_bottom, button_width, button_height])
        self.btn_delete_last = Button(self.ax_delete_last, '删除最后标记')
        self.btn_delete_last.on_clicked(self.delete_last_segment)
        
        # 打开姿态数据窗口按钮
        self.ax_data_window = plt.axes([0.05 + (button_width + button_spacing) * 4, button_bottom, button_width, button_height])
        self.btn_data_window = Button(self.ax_data_window, '姿态数据窗口')
        self.btn_data_window.on_clicked(self.open_data_window)
        
        # 动作选择器 - 移到左侧不遮挡平面图
        self.ax_radio = plt.axes([0.01, 0.5, 0.08, 0.35])
        self.radio = RadioButtons(
            self.ax_radio, 
            list(self.action_colors.keys()),
            activecolor='blue'
        )
        self.radio.on_clicked(self.set_action)
        
        # 添加标题显示当前选择的动作
        self.action_title = self.fig.text(0.09, 0.88, f"当前动作: {self.current_action}", 
                                         fontsize=10, bbox=dict(facecolor='white', alpha=0.5))
        
        # 添加说明文本
        self.ax_text = plt.axes([0.6, button_bottom, 0.35, button_height*2])
        self.ax_text.text(0, 0.5, 
                         '使用说明:\n1. 载入数据  2. 选择动作类型  3. 在时间线上拖动选择区域  4. 保存结果\n* 可清除所有标记或删除最后一个标记',
                         fontsize=9)
        self.ax_text.axis('off')
        
        # 初始化span选择器
        self.span_selector = None  # 将在加载数据后初始化
        
        # 显示窗口
        # 避免tight_layout警告
        plt.subplots_adjust(bottom=0.15, wspace=0.3, hspace=0.3, left=0.1, right=0.95, top=0.95)
        plt.show()
    
    def load_data(self, event):
        """载入飞行轨迹数据文件"""
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 打开文件选择对话框
        file_path = filedialog.askopenfilename(
            title="选择飞行轨迹数据文件",
            filetypes=[("CSV Files", "*.csv"), ("All Files", "*.*")]
        )
        
        if not file_path:
            return
        
        self.file_path = file_path
        self.file_name = Path(file_path).stem
        
        try:
            # 读取数据
            self.df = pd.read_csv(file_path)
            
            # 检查必要的列
            required_cols = ['Longitude', 'Latitude', 'Altitude']
            if not all(col in self.df.columns for col in required_cols):
                missing = [col for col in required_cols if col not in self.df.columns]
                raise ValueError(f"缺少必要的列: {missing}")
            
            # 重置索引
            self.df = self.df.reset_index(drop=True)
            
            # 添加动作标记列
            self.df['Manual_Action'] = ''
            self.df['Manual_Action_Code'] = ''
            
            # 初始化变量
            self.current_index = 0
            self.span_start_index = -1
            self.action_segments = []
            
            # 更新图表
            self.update_plots()
            
            # 初始化span选择器
            self.setup_span_selector()
            
            # 如果数据窗口已打开，更新它
            if self.data_window is not None and plt.fignum_exists(self.data_window.number):
                self.open_data_window(None)
            
            # 添加主窗口鼠标事件以实现与数据窗口同步
            self.fig.canvas.mpl_connect('button_press_event', self.on_main_window_click)
            
            print(f"成功加载数据: {self.file_name}, 共{len(self.df)}个数据点")
            
        except Exception as e:
            print(f"加载数据时出错: {str(e)}")
    
    def setup_span_selector(self):
        """设置时间线上的区域选择器"""
        # 清除之前的时间线图表
        self.ax_timeline.clear()
        
        # 在时间线上绘制高度或速度数据
        if 'Altitude' in self.df.columns:
            self.ax_timeline.plot(self.df.index, self.df['Altitude'], 'b-', linewidth=0.7)
            self.ax_timeline.set_ylabel('高度')
        elif 'GS' in self.df.columns:  # 地速
            self.ax_timeline.plot(self.df.index, self.df['GS'], 'b-', linewidth=0.7)
            self.ax_timeline.set_ylabel('地速')
        
        self.ax_timeline.set_xlabel('数据点索引')
        self.ax_timeline.set_title('拖动选择要标记的区域')
        
        # 添加十字光标到时间线
        self.timeline_cursor = Cursor(self.ax_timeline, useblit=True, color='red', linewidth=1)
        
        # 添加数据显示文本框
        if not hasattr(self, 'timeline_text') or self.timeline_text is None:
            self.timeline_text = self.fig.text(0.5, 0.3, "", 
                                         bbox=dict(facecolor='white', alpha=0.9, edgecolor='gray', boxstyle='round'),
                                         ha='center', fontsize=9)
        self.timeline_text.set_visible(False)  # 初始时不显示
        
        # 创建span选择器
        self.span_selector = SpanSelector(
            self.ax_timeline, 
            self.on_span_select,
            'horizontal', 
            useblit=True,
            props=dict(alpha=0.5, facecolor=self.action_colors[self.current_action])
        )
        
        # 如果有已经标记的段，显示它们
        self.display_marked_segments()
        
        # 添加时间线点击事件，用于删除特定区域标记
        self.ax_timeline.figure.canvas.mpl_connect('button_press_event', self.on_timeline_click)
        
        # 添加鼠标移动事件，用于显示当前点信息
        self.ax_timeline.figure.canvas.mpl_connect('motion_notify_event', self.on_timeline_hover)
        
        plt.draw()
    
    def on_timeline_click(self, event):
        """处理时间线上的点击事件，用于删除点击位置的区域标记"""
        # 检查点击是否在时间线区域内
        if event.inaxes != self.ax_timeline or self.df is None or not self.action_segments:
            return
        
        # 右键点击删除标记
        if event.button == 3:  # 右键
            x = int(event.xdata)
            
            # 查找包含该点的段
            segment_to_remove = None
            for i, segment in enumerate(self.action_segments):
                if segment['start'] <= x <= segment['end']:
                    segment_to_remove = i
                    break
            
            # 如果找到了包含该点的段，删除它
            if segment_to_remove is not None:
                segment = self.action_segments.pop(segment_to_remove)
                # 清除该段的标记
                self.df.loc[segment['start']:segment['end'], 'Manual_Action'] = ''
                self.df.loc[segment['start']:segment['end'], 'Manual_Action_Code'] = ''
                
                # 更新显示
                self.display_marked_segments()
                self.update_plots()
                
                print(f"删除了 {segment['start']} 到 {segment['end']} 的 {segment['action']} 标记")
    
    def on_span_select(self, xmin, xmax):
        """当用户在时间线上选择一段区域时调用"""
        # 转换为整数索引
        xmin = int(np.floor(xmin))
        xmax = int(np.ceil(xmax))
        
        # 限制在有效范围内
        xmin = max(0, xmin)
        xmax = min(len(self.df) - 1, xmax)
        
        if xmax <= xmin:
            return
        
        # 记录新的动作段
        self.action_segments.append({
            'start': xmin,
            'end': xmax,
            'action': self.current_action,
            'action_code': self.action_codes[self.current_action]
        })
        
        # 更新数据框中的标记
        self.df.loc[xmin:xmax, 'Manual_Action'] = self.current_action
        self.df.loc[xmin:xmax, 'Manual_Action_Code'] = self.action_codes[self.current_action]
        
        # 更新显示
        self.display_marked_segments()
        self.update_plots()
        
        print(f"标记了 {self.current_action} 动作: 点 {xmin} 到 {xmax}")
    
    def delete_last_segment(self, event):
        """删除最后一个标记的段"""
        if not self.action_segments:
            print("没有标记可删除")
            return
        
        # 获取最后一个段
        last_segment = self.action_segments.pop()
        
        # 清除该段的标记
        self.df.loc[last_segment['start']:last_segment['end'], 'Manual_Action'] = ''
        self.df.loc[last_segment['start']:last_segment['end'], 'Manual_Action_Code'] = ''
        
        # 更新显示
        self.display_marked_segments()
        self.update_plots()
        
        print(f"删除了最后标记的 {last_segment['action']} 动作: 点 {last_segment['start']} 到 {last_segment['end']}")
    
    def display_marked_segments(self):
        """在时间线上显示已标记的区域"""
        # 清除现有的标记显示
        for collection in self.ax_timeline.collections:
            collection.remove()
        
        # 为每个标记的段添加背景色
        for segment in self.action_segments:
            self.ax_timeline.axvspan(
                segment['start'], 
                segment['end'],
                alpha=0.3,
                color=self.action_colors[segment['action']]
            )
        
        # 如果数据窗口已打开，也更新它上面的标记
        if self.data_window is not None and plt.fignum_exists(self.data_window.number):
            for ax_name, ax in self.data_axes.items():
                # 清除现有标记
                for collection in ax.collections:
                    if not hasattr(collection, 'tag') or collection.tag != 'highlight_point':
                        collection.remove()
                
                # 重新显示标记
                self.display_marked_segments_on_data(ax)
        
        plt.draw()
    
    def update_plots(self):
        """更新3D和2D图表"""
        if self.df is None:
            return
        
        # 清除现有图表
        self.ax_3d.clear()
        self.ax_2d.clear()
        
        # 3D轨迹图
        self.ax_3d.plot(self.df['Longitude'], self.df['Latitude'], self.df['Altitude'], 
                'b-', linewidth=1, alpha=0.5, label='完整轨迹')
        
        # 标记已标记的动作
        for action_name in self.action_colors:
            mask = self.df['Manual_Action'] == action_name
            if mask.any():
                self.ax_3d.scatter(
                    self.df.loc[mask, 'Longitude'], 
                    self.df.loc[mask, 'Latitude'], 
                    self.df.loc[mask, 'Altitude'],
                    color=self.action_colors[action_name], 
                    s=5, 
                    alpha=0.8, 
                    label=action_name
                )
        
        # 设置3D图的标签
        self.ax_3d.set_xlabel('经度')
        self.ax_3d.set_ylabel('纬度')
        self.ax_3d.set_zlabel('高度 (m)')
        self.ax_3d.set_title('三维轨迹图')
        self.ax_3d.legend(loc='upper right')
        
        # 2D平面图
        self.ax_2d.plot(self.df['Longitude'], self.df['Latitude'], 
              'b-', linewidth=1, alpha=0.5, label='完整轨迹')
        
        # 标记已标记的动作
        for action_name in self.action_colors:
            mask = self.df['Manual_Action'] == action_name
            if mask.any():
                self.ax_2d.scatter(
                    self.df.loc[mask, 'Longitude'], 
                    self.df.loc[mask, 'Latitude'],
                    color=self.action_colors[action_name], 
                    s=5, 
                    alpha=0.8, 
                    label=action_name
                )
        
        # 设置2D图的标签
        self.ax_2d.set_xlabel('经度')
        self.ax_2d.set_ylabel('纬度')
        self.ax_2d.set_title('二维平面图')
        self.ax_2d.grid(True, linestyle='--', alpha=0.5)
        
        # 更新图例，使用紧凑布局
        self.ax_2d.legend(loc='upper right', fontsize='small', bbox_to_anchor=(1, 1), 
                         frameon=True, fancybox=True, framealpha=0.7, ncol=2)
        
        # 如果当前有选中的点，重新高亮显示
        if hasattr(self, 'current_index') and self.current_index > 0:
            self.highlight_point_in_main_window(self.current_index)
        
        plt.draw()
    
    def set_action(self, label):
        """设置当前选择的动作类型"""
        self.current_action = label
        
        # 更新span选择器的颜色
        if self.span_selector:
            self.span_selector.rectprops['facecolor'] = self.action_colors[self.current_action]
            plt.draw()
        
        # 更新显示当前动作的标题
        self.action_title.set_text(f"当前动作: {self.current_action}")
        
        print(f"当前选择的动作类型: {self.current_action}")
    
    def save_results(self, event):
        """保存标记结果"""
        if self.df is None or len(self.action_segments) == 0:
            print("没有数据或标记可保存")
            return
        
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 获取要保存的目录
        save_dir = filedialog.askdirectory(
            title="选择保存目录"
        )
        
        if not save_dir:
            return
        
        # 将路径转换为Path对象
        save_path = Path(save_dir)
        
        try:
            # 保存完整的带标记数据
            full_data_path = save_path / f"{self.file_name}_manual_marked.csv"
            self.df.to_csv(full_data_path, index=False)
            
            # 为每种动作类型单独保存数据段
            for action_name in self.action_colors.keys():
                action_mask = self.df['Manual_Action'] == action_name
                if action_mask.any():
                    action_code = self.action_codes[action_name]
                    action_df = self.df[action_mask]
                    action_path = save_path / f"{self.file_name}_{action_name}_{action_code}.csv"
                    action_df.to_csv(action_path, index=False)
            
            # 保存段信息为单独的CSV
            segments_df = pd.DataFrame(self.action_segments)
            segments_path = save_path / f"{self.file_name}_segments.csv"
            segments_df.to_csv(segments_path, index=False)
            
            # 创建和保存标记结果的可视化
            self.save_visualization(save_path)
            
            print(f"已保存标记结果到 {save_path}")
            
        except Exception as e:
            print(f"保存结果时出错: {str(e)}")
    
    def save_visualization(self, save_path):
        """保存标记结果的可视化图表"""
        # 3D视图
        fig_3d = plt.figure(figsize=(12, 10))
        ax_3d = fig_3d.add_subplot(111, projection='3d')
        
        # 绘制完整轨迹
        ax_3d.plot(self.df['Longitude'], self.df['Latitude'], self.df['Altitude'], 
              'b-', linewidth=1, alpha=0.3, label='完整轨迹')
        
        # 标记不同动作
        for action_name in self.action_colors:
            mask = self.df['Manual_Action'] == action_name
            if mask.any():
                ax_3d.scatter(
                    self.df.loc[mask, 'Longitude'], 
                    self.df.loc[mask, 'Latitude'], 
                    self.df.loc[mask, 'Altitude'],
                    color=self.action_colors[action_name], 
                    s=10, 
                    alpha=0.8, 
                    label=action_name
                )
        
        ax_3d.set_xlabel('经度')
        ax_3d.set_ylabel('纬度')
        ax_3d.set_zlabel('高度 (m)')
        ax_3d.set_title(f'手动标记的飞行动作 - {self.file_name}')
        ax_3d.legend()
        
        # 保存3D图
        fig_3d.savefig(save_path / f"{self.file_name}_3d_marked.png", dpi=100, bbox_inches='tight')
        plt.close(fig_3d)
        
        # 2D平面图
        fig_2d = plt.figure(figsize=(12, 10))
        ax_2d = fig_2d.add_subplot(111)
        
        # 绘制完整轨迹
        ax_2d.plot(self.df['Longitude'], self.df['Latitude'], 
              'b-', linewidth=1, alpha=0.3, label='完整轨迹')
        
        # 标记不同动作
        for action_name in self.action_colors:
            mask = self.df['Manual_Action'] == action_name
            if mask.any():
                ax_2d.scatter(
                    self.df.loc[mask, 'Longitude'], 
                    self.df.loc[mask, 'Latitude'],
                    color=self.action_colors[action_name], 
                    s=10, 
                    alpha=0.8, 
                    label=action_name
                )
        
        ax_2d.set_xlabel('经度')
        ax_2d.set_ylabel('纬度')
        ax_2d.set_title(f'手动标记的飞行动作 (2D) - {self.file_name}')
        ax_2d.grid(True, linestyle='--', alpha=0.5)
        ax_2d.legend()
        
        # 保存2D图
        fig_2d.savefig(save_path / f"{self.file_name}_2d_marked.png", dpi=100, bbox_inches='tight')
        plt.close(fig_2d)
        
        # 高度剖面图和标记
        fig_profile = plt.figure(figsize=(15, 7))
        ax_profile = fig_profile.add_subplot(111)
        
        # 绘制高度剖面
        if 'Altitude' in self.df.columns:
            ax_profile.plot(self.df.index, self.df['Altitude'], 'b-', linewidth=1, alpha=0.5)
            
            # 为不同动作添加背景色
            for action_name in self.action_colors:
                mask = self.df['Manual_Action'] == action_name
                if mask.any():
                    ax_profile.fill_between(
                        self.df.index, 
                        0, 
                        self.df['Altitude'],
                        where=mask,
                        color=self.action_colors[action_name],
                        alpha=0.3,
                        label=action_name
                    )
        
        ax_profile.set_xlabel('数据点索引')
        ax_profile.set_ylabel('高度 (m)')
        ax_profile.set_title(f'高度剖面与动作标记 - {self.file_name}')
        ax_profile.legend()
        ax_profile.grid(True, linestyle='--', alpha=0.5)
        
        # 保存高度剖面图
        fig_profile.savefig(save_path / f"{self.file_name}_profile_marked.png", dpi=100, bbox_inches='tight')
        plt.close(fig_profile)
        
        # 动作分布饼图
        action_counts = self.df['Manual_Action'].value_counts()
        if not action_counts.empty:
            fig_pie = plt.figure(figsize=(10, 8))
            ax_pie = fig_pie.add_subplot(111)
            
            wedges, texts, autotexts = ax_pie.pie(
                action_counts,
                labels=action_counts.index,
                autopct='%1.1f%%',
                colors=[self.action_colors.get(action, 'gray') for action in action_counts.index],
                textprops={'fontsize': 12}
            )
            
            ax_pie.set_title(f'手动标记的动作分布 - {self.file_name}')
            
            # 保存饼图
            fig_pie.savefig(save_path / f"{self.file_name}_pie_marked.png", dpi=100, bbox_inches='tight')
            plt.close(fig_pie)
    
    def clear_marks(self, event):
        """清除所有标记"""
        if self.df is None:
            return
        
        # 清空动作段列表
        self.action_segments = []
        
        # 重置DataFrame中的标记
        self.df['Manual_Action'] = ''
        self.df['Manual_Action_Code'] = ''
        
        # 更新显示
        self.display_marked_segments()
        self.update_plots()
        
        print("已清除所有标记")

    def open_data_window(self, event):
        """打开包含姿态数据的窗口"""
        if self.df is None:
            print("请先载入数据")
            return
        
        # 检查需要的列是否存在
        data_columns = ['Roll', 'Pitch', 'VSpd']
        available_columns = [col for col in data_columns if col in self.df.columns]
        
        if not available_columns:
            print("数据中没有Roll、Pitch或VSpd列")
            return
        
        # 如果窗口已存在，关闭它
        if self.data_window is not None:
            plt.close(self.data_window)
        
        # 创建新窗口
        self.data_window = plt.figure(figsize=(12, 8))
        self.data_window.canvas.manager.set_window_title('飞行姿态数据')
        
        # 根据可用数据列创建子图
        n_plots = len(available_columns) + 1  # +1 for cursor position data
        self.data_axes = {}
        
        for i, col in enumerate(available_columns):
            ax = self.data_window.add_subplot(n_plots, 1, i+1)
            ax.plot(self.df.index, self.df[col], lw=1.5)
            ax.set_ylabel(col)
            ax.grid(True, linestyle='--', alpha=0.7)
            
            # 为每个子图添加光标
            cursor = Cursor(ax, useblit=True, color='red', linewidth=1)
            self.cursors[col] = cursor
            
            # 保存轴引用
            self.data_axes[col] = ax
            
            # 如果有标记，在数据图上显示
            self.display_marked_segments_on_data(ax)
        
        # 添加文本显示区域在底部
        self.info_ax = self.data_window.add_subplot(n_plots, 1, n_plots)
        self.info_ax.axis('off')
        self.data_display_text = self.info_ax.text(0.05, 0.5, "点击图表查看数据", transform=self.info_ax.transAxes)
        
        # 连接鼠标事件
        self.data_window.canvas.mpl_connect('motion_notify_event', self.on_data_window_hover)
        self.data_window.canvas.mpl_connect('button_press_event', self.on_data_window_click)
        
        # 调整布局
        plt.tight_layout()
        plt.subplots_adjust(hspace=0.3)
        
        # 显示窗口
        self.data_window.show()
        
    def display_marked_segments_on_data(self, ax):
        """在数据窗口显示已标记的区域"""
        if not self.action_segments:
            return
            
        # 保存当前ylim
        ylim = ax.get_ylim()
        
        # 为每个标记的段添加背景色
        for segment in self.action_segments:
            ax.axvspan(
                segment['start'], 
                segment['end'],
                alpha=0.2,
                color=self.action_colors[segment['action']]
            )
        
        # 恢复ylim
        ax.set_ylim(ylim)

    def on_data_window_hover(self, event):
        """处理数据窗口中的鼠标悬停事件"""
        if event.inaxes is None:
            return
            
        # 获取当前x位置（数据索引）
        x_index = int(event.xdata) if event.xdata is not None else 0
        
        # 确保索引在有效范围内
        if x_index < 0 or x_index >= len(self.df):
            return
            
        # 更新当前索引
        self.current_index = x_index
        
        # 更新显示的文本
        text = f"索引: {x_index}"
        
        # 添加经纬度高度信息
        if 'Longitude' in self.df.columns:
            text += f" | 经度: {self.df.iloc[x_index]['Longitude']:.6f}"
        if 'Latitude' in self.df.columns:
            text += f" | 纬度: {self.df.iloc[x_index]['Latitude']:.6f}"
        if 'Altitude' in self.df.columns:
            text += f" | 高度: {self.df.iloc[x_index]['Altitude']:.1f}m"
        
        # 添加姿态数据
        data_columns = ['Roll', 'Pitch', 'VSpd', 'HDG', 'GS']
        for col in data_columns:
            if col in self.df.columns:
                text += f" | {col}: {self.df.iloc[x_index][col]:.2f}"
        
        # 更新文本显示
        if self.data_display_text:
            self.data_display_text.set_text(text)
            self.data_window.canvas.draw_idle()
            
        # 同步主窗口的三维图和二维图位置
        self.highlight_point_in_main_window(x_index)
    
    def on_data_window_click(self, event):
        """处理数据窗口中的鼠标点击事件"""
        if event.inaxes is None:
            return
            
        # 获取点击位置
        x_index = int(event.xdata) if event.xdata is not None else 0
        
        # 确保索引在有效范围内
        if x_index < 0 or x_index >= len(self.df):
            return
            
        # 更新当前索引
        self.current_index = x_index
        
        # 选中该点
        print(f"选中点索引: {x_index}")
        
        # 双击开始划定区域
        if event.dblclick:
            print(f"设置区域开始点: {x_index}")
            self.start_span_selection(x_index)
    
    def highlight_point_in_main_window(self, index):
        """在主窗口中高亮显示指定索引的点"""
        if self.df is None or index < 0 or index >= len(self.df):
            return
            
        # 清除之前的高亮点
        for ax in [self.ax_3d, self.ax_2d]:
            for artist in ax.collections:
                if getattr(artist, 'tag', None) == 'highlight_point':
                    artist.remove()
        
        # 获取点坐标
        point = self.df.iloc[index]
        
        # 高亮3D图上的点
        if 'Longitude' in self.df.columns and 'Latitude' in self.df.columns and 'Altitude' in self.df.columns:
            scatter = self.ax_3d.scatter(
                [point['Longitude']], 
                [point['Latitude']], 
                [point['Altitude']],
                color='yellow', 
                s=100, 
                edgecolor='black',
                linewidth=1.5,
                alpha=0.8
            )
            scatter.tag = 'highlight_point'
            
        # 高亮2D图上的点
        if 'Longitude' in self.df.columns and 'Latitude' in self.df.columns:
            scatter = self.ax_2d.scatter(
                [point['Longitude']], 
                [point['Latitude']],
                color='yellow', 
                s=100, 
                edgecolor='black',
                linewidth=1.5,
                alpha=0.8
            )
            scatter.tag = 'highlight_point'
        
        # 更新显示
        self.fig.canvas.draw_idle()
        
    def start_span_selection(self, start_index):
        """在数据窗口中开始区域选择"""
        # 保存起始索引以便后续使用
        self.span_start_index = start_index

    def on_main_window_click(self, event):
        """处理主窗口中的鼠标点击，与数据窗口同步"""
        # 只处理时间线区域的点击
        if event.inaxes == self.ax_timeline:
            # 获取点击位置
            x_index = int(event.xdata) if event.xdata is not None else 0
            
            # 确保索引在有效范围内
            if x_index < 0 or x_index >= len(self.df):
                return
                
            # 更新当前索引
            self.current_index = x_index
            
            # 高亮显示该点
            self.highlight_point_in_main_window(x_index)
            
            # 如果数据窗口已打开，同步选中位置
            if self.data_window is not None and plt.fignum_exists(self.data_window.number):
                # 在每个数据轴上添加垂直线表示当前位置
                for ax_name, ax in self.data_axes.items():
                    # 移除旧的位置线
                    for line in ax.lines:
                        if getattr(line, 'tag', None) == 'position_line':
                            line.remove()
                    
                    # 添加新的位置线
                    line = ax.axvline(x_index, color='r', linestyle='--', alpha=0.7)
                    line.tag = 'position_line'
                
                # 更新数据文本显示
                text = f"索引: {x_index}"
                
                # 添加数据值
                data_columns = ['Longitude', 'Latitude', 'Altitude', 'Roll', 'Pitch', 'VSpd', 'HDG', 'GS']
                for col in data_columns:
                    if col in self.df.columns:
                        val = self.df.iloc[x_index][col]
                        if col in ['Longitude', 'Latitude']:
                            text += f" | {col}: {val:.6f}"
                        else:
                            text += f" | {col}: {val:.2f}"
                
                # 更新文本
                if self.data_display_text:
                    self.data_display_text.set_text(text)
                
                # 更新图表
                self.data_window.canvas.draw_idle()

    def on_timeline_hover(self, event):
        """处理时间线上的鼠标悬停事件"""
        if event.inaxes != self.ax_timeline or self.df is None:
            self.timeline_text.set_visible(False)
            self.fig.canvas.draw_idle()
            return
            
        # 获取当前x位置（数据索引）
        x_index = int(event.xdata) if event.xdata is not None else 0
        
        # 确保索引在有效范围内
        if x_index < 0 or x_index >= len(self.df):
            self.timeline_text.set_visible(False)
            self.fig.canvas.draw_idle()
            return
            
        # 更新当前索引
        self.current_index = x_index
        
        # 构建显示文本
        text = f"索引: {x_index}"
        
        # 添加数据
        if 'Altitude' in self.df.columns:
            text += f" | 高度: {self.df.iloc[x_index]['Altitude']:.1f}m"
        if 'VSpd' in self.df.columns:
            text += f" | 垂速: {self.df.iloc[x_index]['VSpd']:.1f}"
        if 'HDG' in self.df.columns:
            text += f" | 航向: {self.df.iloc[x_index]['HDG']:.1f}°"
        if 'Roll' in self.df.columns:
            text += f" | 滚转: {self.df.iloc[x_index]['Roll']:.1f}°"
        if 'Pitch' in self.df.columns:
            text += f" | 俯仰: {self.df.iloc[x_index]['Pitch']:.1f}°"
        
        # 更新文本
        self.timeline_text.set_text(text)
        self.timeline_text.set_visible(True)
        
        # 计算更好的文本位置，避免超出图表边界
        # 使用图表坐标而不是数据坐标
        x_pos = event.x / self.fig.dpi / self.fig.get_figwidth()
        y_pos = (event.y + 30) / self.fig.dpi / self.fig.get_figheight()  # 在鼠标上方30像素处
        self.timeline_text.set_position((x_pos, y_pos))
        self.timeline_text.set_transform(self.fig.transFigure)
        
        # 高亮显示该点
        self.highlight_point_in_main_window(x_index)
        
        # 添加垂直指示线
        # 清除之前的线
        for line in self.ax_timeline.lines:
            if getattr(line, 'tag', None) == 'hover_line':
                line.remove()
        
        # 添加新的垂直线标记当前位置
        line = self.ax_timeline.axvline(x_index, color='red', linestyle='--', alpha=0.5, linewidth=0.8)
        line.tag = 'hover_line'
        
        # 更新图表
        self.fig.canvas.draw_idle()

if __name__ == '__main__':
    app = FlightActionMarker() 