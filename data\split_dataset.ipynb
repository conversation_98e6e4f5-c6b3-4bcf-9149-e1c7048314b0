{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["——划分训练集验证集测试集，6/2/2——"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import os\n", "import re\n", "from sklearn.model_selection import train_test_split"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# --- 配置参数 ---\n", "SOURCE_FILE = 'D:/1workfile/MYcode/informer1/informer_anomaly/data/起落航线/qiluo_筛选.csv'\n", "OUTPUT_DIR = 'D:/1workfile/MYcode/informer1/informer_anomaly/data/起落航线'\n", "TRAIN_FILE_PATH = os.path.join(OUTPUT_DIR, 'train_set.csv')\n", "VALIDATION_FILE_PATH = os.path.join(OUTPUT_DIR, 'validation_set.csv')\n", "TEST_FILE_PATH = os.path.join(OUTPUT_DIR, 'test_set.csv')\n", "VALIDATION_PLUS_TEST_SIZE = 0.4 # 验证集+测试集占40%\n", "TEST_SPLIT_FROM_REST = 0.5 # 从剩余的40%中，分一半（即20%）给测试集\n", "RANDOM_STATE = 42 # 随机种子，确保每次划分结果一致"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["源文件: D:/1workfile/MYcode/informer1/informer_anomaly/data/起落航线/qiluo_筛选.csv\n", "输出目录: D:/1workfile/MYcode/informer1/informer_anomaly/data/起落航线\n"]}], "source": ["# --- 确保输出目录存在 ---\n", "os.makedirs(OUTPUT_DIR, exist_ok=True)\n", "print(f\"源文件: {SOURCE_FILE}\")\n", "print(f\"输出目录: {OUTPUT_DIR}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始加载数据并识别段...\n", "成功识别出 39 个爬升段。\n", "\n", "划分完成 (比例约为 8:1:1): \n", "训练集包含 23 个段。\n", "验证集包含 8 个段。\n", "测试集包含 8 个段。\n", "\n", "训练集总行数: 10498\n", "验证集总行数: 3611\n", "测试集总行数: 3629\n", "\n", "训练集已保存到: D:/1workfile/MYcode/informer1/informer_anomaly/data/起落航线\\train_set.csv\n", "验证集已保存到: D:/1workfile/MYcode/informer1/informer_anomaly/data/起落航线\\validation_set.csv\n", "测试集已保存到: D:/1workfile/MYcode/informer1/informer_anomaly/data/起落航线\\test_set.csv\n"]}], "source": ["def load_and_identify_segments(file_path):\n", "    \"\"\"加载数据并按段分割\"\"\"\n", "    print(\"开始加载数据并识别段...\")\n", "    try:\n", "        # 尝试用不同编码读取\n", "        df = pd.read_csv(file_path, encoding='utf-8')\n", "    except UnicodeDecodeError:\n", "        df = pd.read_csv(file_path, encoding='gbk')\n", "    \n", "    if '段标记' not in df.columns:\n", "        raise ValueError(\"文件中未找到 '段标记' 列\")\n", "        \n", "    segments = []\n", "    current_segment_data = []\n", "    \n", "    # 遍历DataFrame，按段分割\n", "    for _, row in df.iterrows():\n", "        marker = row['段标记']\n", "        # 遇到新段的起点\n", "        if pd.notna(marker) and '起点' in marker:\n", "            # 如果当前段有数据，说明上一个段结束了，保存它\n", "            if current_segment_data:\n", "                segments.append(pd.DataFrame(current_segment_data))\n", "            # 开始记录新段\n", "            current_segment_data = [row]\n", "        # 遇到段的终点\n", "        elif pd.notna(marker) and '终点' in marker:\n", "            # 将终点行加入，并保存这个完整的段\n", "            if current_segment_data:\n", "                current_segment_data.append(row)\n", "                segments.append(pd.DataFrame(current_segment_data))\n", "            # 清空，为下一个段做准备\n", "            current_segment_data = []\n", "        # 普通数据行\n", "        elif current_segment_data:\n", "            current_segment_data.append(row)\n", "            \n", "    # 如果文件末尾没有终点标记，保存最后一个段\n", "    if current_segment_data:\n", "        segments.append(pd.DataFrame(current_segment_data))\n", "        \n", "    print(f\"成功识别出 {len(segments)} 个爬升段。\")\n", "    return segments\n", "\n", "# --- 执行划分 ---\n", "try:\n", "    # 1. 加载并识别所有段\n", "    all_segments = load_and_identify_segments(SOURCE_FILE)\n", "\n", "    if all_segments:\n", "        # 2. 第一次划分：分出训练集和剩余部分\n", "        train_segments, temp_segments = train_test_split(\n", "            all_segments, \n", "            test_size=VALIDATION_PLUS_TEST_SIZE, \n", "            random_state=RANDOM_STATE\n", "        )\n", "        \n", "        # 3. 第二次划分：将剩余部分对半分为验证集\n", "        val_segments, test_segments = train_test_split(\n", "            temp_segments, \n", "            test_size=TEST_SPLIT_FROM_REST, \n", "            random_state=RANDOM_STATE # 使用相同的随机种子保证一致性\n", "        )\n", "\n", "        print(f\"\\n划分完成 (比例约为 8:1:1): \")\n", "        print(f\"训练集包含 {len(train_segments)} 个段。\")\n", "        print(f\"验证集包含 {len(val_segments)} 个段。\")\n", "        print(f\"测试集包含 {len(test_segments)} 个段。\")\n", "\n", "        # 4. 合并段列表为大的DataFrame\n", "        train_df = pd.concat(train_segments, ignore_index=True)\n", "        validation_df = pd.concat(val_segments, ignore_index=True)\n", "        test_df = pd.concat(test_segments, ignore_index=True)\n", "\n", "        print(f\"\\n训练集总行数: {len(train_df)}\")\n", "        print(f\"验证集总行数: {len(validation_df)}\")\n", "        print(f\"测试集总行数: {len(test_df)}\")\n", "\n", "        # 5. 保存到CSV文件\n", "        train_df.to_csv(TRAIN_FILE_PATH, index=False, encoding='utf-8')\n", "        print(f\"\\n训练集已保存到: {TRAIN_FILE_PATH}\")\n", "\n", "        validation_df.to_csv(VALIDATION_FILE_PATH, index=False, encoding='utf-8')\n", "        print(f\"验证集已保存到: {VALIDATION_FILE_PATH}\")\n", "\n", "        test_df.to_csv(TEST_FILE_PATH, index=False, encoding='utf-8')\n", "        print(f\"测试集已保存到: {TEST_FILE_PATH}\")\n", "\n", "except Exception as e:\n", "    print(f\"\\n处理过程中发生错误: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "LSTM_AE_MY", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 2}